// Product Variant Routes - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 7: RESTful API Endpoint yapısı
// ✅ Kural 8: Authentication/Authorization Pattern
// ✅ Kural 5: Validation Middleware kullanımı

import { Router } from 'express'
import {
  getVariants,
  getVariantById,
  createVariant,
  updateVariant,
  deleteVariant
} from '../controllers/variantController'
import { 
  authenticateToken, 
  requireManager, 
  requireAdmin 
} from '../middlewares/authMiddleware'
import {
  validateBody,
  validateQuery,
  validateParams,
  createVariantSchema,
  updateVariantSchema,
  variantQuerySchema,
  productIdSchema,
  variantIdSchema
} from '../validators/variantValidators'
import rateLimit from 'express-rate-limit'

const router = Router()

// ✅ Kural 8: Rate limiting
const variantLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 dakika
  max: 100, // Her IP için maksimum 100 istek
  message: {
    success: false,
    error: 'Çok fazla istek gönderildi, lütfen daha sonra tekrar deneyin',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false
})

const variantModifyLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 dakika
  max: 50, // Her IP için maksimum 50 değişiklik
  message: {
    success: false,
    error: 'Çok fazla değişiklik yapıldı, lütfen daha sonra tekrar deneyin',
    code: 'MODIFY_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false
})

/**
 * ✅ Kural 7: GET /api/products/:productId/variants - Ürün varyantları listesi (paginated)
 * @desc    Belirli bir ürüne ait varyantları listele
 * @access  Private (Manager+)
 */
router.get(
  '/products/:productId/variants',
  variantLimiter,
  authenticateToken,
  requireManager,
  validateParams(productIdSchema),
  validateQuery(variantQuerySchema),
  getVariants
)

/**
 * ✅ Kural 7: GET /api/variants/:variantId - Varyant detayı
 * @desc    Belirli bir varyantın detaylarını getir
 * @access  Private (Manager+)
 */
router.get(
  '/variants/:variantId',
  variantLimiter,
  authenticateToken,
  requireManager,
  validateParams(variantIdSchema),
  getVariantById
)

/**
 * ✅ Kural 7: POST /api/products/:productId/variants - Yeni varyant oluştur
 * @desc    Belirli bir ürün için yeni varyant oluştur
 * @access  Private (Manager+)
 */
router.post(
  '/products/:productId/variants',
  variantModifyLimiter,
  authenticateToken,
  requireManager,
  validateParams(productIdSchema),
  validateBody(createVariantSchema),
  createVariant
)

/**
 * ✅ Kural 7: PUT /api/variants/:variantId - Varyant güncelle
 * @desc    Belirli bir varyantı güncelle
 * @access  Private (Manager+)
 */
router.put(
  '/variants/:variantId',
  variantModifyLimiter,
  authenticateToken,
  requireManager,
  validateParams(variantIdSchema),
  validateBody(updateVariantSchema),
  updateVariant
)

/**
 * ✅ Kural 7: DELETE /api/variants/:variantId - Varyant sil (soft delete)
 * @desc    Belirli bir varyantı sil
 * @access  Private (Manager+)
 */
router.delete(
  '/variants/:variantId',
  variantModifyLimiter,
  authenticateToken,
  requireManager,
  validateParams(variantIdSchema),
  deleteVariant
)

export default router
