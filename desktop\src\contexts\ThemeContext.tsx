import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles'
import { lightTheme, darkTheme, lightThemeEN, darkThemeEN } from '../theme'
import { useTranslation } from 'react-i18next'

type ThemeMode = 'light' | 'dark'

interface ThemeContextType {
  themeMode: ThemeMode
  toggleTheme: () => void
  setThemeMode: (mode: ThemeMode) => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

interface ThemeProviderProps {
  children: ReactNode
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { i18n } = useTranslation()
  const [themeMode, setThemeMode] = useState<ThemeMode>(() => {
    // LocalStorage'dan tema tercihini al
    const savedTheme = localStorage.getItem('themeMode')
    return (savedTheme as ThemeMode) || 'light'
  })

  // Te<PERSON>ğiştiğinde localStorage'a kaydet
  useEffect(() => {
    localStorage.setItem('themeMode', themeMode)
  }, [themeMode])

  const toggleTheme = () => {
    setThemeMode(prev => prev === 'light' ? 'dark' : 'light')
  }

  // Dil ve tema kombinasyonuna göre doğru temayı seç
  const getTheme = () => {
    const isEnglish = i18n.language === 'en'
    
    if (themeMode === 'dark') {
      return isEnglish ? darkThemeEN : darkTheme
    } else {
      return isEnglish ? lightThemeEN : lightTheme
    }
  }

  const value: ThemeContextType = {
    themeMode,
    toggleTheme,
    setThemeMode,
  }

  return (
    <ThemeContext.Provider value={value}>
      <MuiThemeProvider theme={getTheme()}>
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  )
}

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
