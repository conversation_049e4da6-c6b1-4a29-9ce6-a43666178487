// Performance Optimized Dashboard Header - DEVELOPMENT_RULES.md Kural 17
// ✅ React.memo, useCallback, useMemo patterns applied

import React, { useState, useCallback, useMemo } from 'react'
import {
  Box,
  Typography,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Divider,
  Tooltip,
  useTheme as useMuiTheme,
} from '@mui/material'
import {
  Wifi,
  WifiOff,
  Cloud,
  CloudOff,
  AccountCircle,
  Logout,
  Store,
  Person,
  LightMode,
  DarkMode
} from '@mui/icons-material'
import { useTranslation } from 'react-i18next'
import { useAuthStore } from '../../store/useAuthStore'
import { useConnectionStatus } from '../../hooks/useConnectionStatus'
import { useTheme } from '../../contexts/ThemeContext'

// ✅ Kural 17: Component props interface
interface DashboardHeaderProps {
  branchName?: string
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  branchName = "Merkez Şube"
}) => {
  const { t, i18n } = useTranslation()
  const muiTheme = useMuiTheme()
  const { themeMode, toggleTheme } = useTheme()
  const { user, logout } = useAuthStore()
  const connectionStatus = useConnectionStatus()

  // User menu state
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

  // ✅ Kural 17: useMemo for computed values
  const isMenuOpen = useMemo(() => Boolean(anchorEl), [anchorEl])

  const currentLanguage = useMemo(() => {
    return i18n.language === 'tr' ? 'TR' : 'EN'
  }, [i18n.language])

  // ✅ Kural 17: useCallback for event handlers
  const toggleLanguage = useCallback(() => {
    const newLang = i18n.language === 'tr' ? 'en' : 'tr'
    i18n.changeLanguage(newLang)
  }, [i18n])

  const handleUserMenuOpen = useCallback((event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }, [])

  const handleUserMenuClose = useCallback(() => {
    setAnchorEl(null)
  }, [])

  const handleLogout = useCallback(async () => {
    handleUserMenuClose()
    await logout()
  }, [logout])

  // ✅ Kural 17: useMemo for expensive calculations and objects
  const connectionData = useMemo(() => {
    const getConnectionIcon = (type: 'internet' | 'backend') => {
      const isConnected = type === 'internet' ? connectionStatus.internet : connectionStatus.backend

      if (type === 'internet') {
        return isConnected ? (
          <Wifi sx={{ color: muiTheme.palette.success.main }} />
        ) : (
          <WifiOff sx={{ color: muiTheme.palette.error.main }} />
        )
      } else {
        return isConnected ? (
          <Cloud sx={{ color: muiTheme.palette.success.main }} />
        ) : (
          <CloudOff sx={{ color: muiTheme.palette.error.main }} />
        )
      }
    }

    const getConnectionText = (type: 'internet' | 'backend') => {
      const isConnected = type === 'internet' ? connectionStatus.internet : connectionStatus.backend

      if (type === 'internet') {
        return isConnected ? t('header.internet.connected') : t('header.internet.disconnected')
      } else {
        return isConnected ? t('header.backend.connected') : t('header.backend.disconnected')
      }
    }

    const getConnectionColor = (type: 'internet' | 'backend') => {
      const isConnected = type === 'internet' ? connectionStatus.internet : connectionStatus.backend
      return isConnected ? 'success' : 'error'
    }

    return {
      internet: {
        icon: getConnectionIcon('internet'),
        text: getConnectionText('internet'),
        color: getConnectionColor('internet')
      },
      backend: {
        icon: getConnectionIcon('backend'),
        text: getConnectionText('backend'),
        color: getConnectionColor('backend')
      }
    }
  }, [connectionStatus.internet, connectionStatus.backend, muiTheme.palette, t])

  // ✅ Kural 17: useMemo for style objects
  const chipStyles = useMemo(() => ({
    fontSize: { xs: '0.7rem', sm: '0.8125rem' },
    height: { xs: 24, sm: 32 }
  }), [])

  const iconButtonStyles = useMemo(() => ({
    language: {
      minWidth: { xs: 36, sm: 40 },
      height: { xs: 28, sm: 32 },
      borderRadius: 1,
      border: `1px solid ${muiTheme.palette.divider}`,
      '&:hover': {
        backgroundColor: 'action.hover'
      }
    },
    theme: {
      border: `1px solid ${muiTheme.palette.divider}`,
      width: { xs: 28, sm: 32 },
      height: { xs: 28, sm: 32 },
      '&:hover': {
        backgroundColor: 'action.hover'
      }
    },
    user: {
      p: { xs: 0.25, sm: 0.5 },
      border: `2px solid ${muiTheme.palette.primary.main}`,
      '&:hover': {
        backgroundColor: 'primary.main',
        '& .MuiAvatar-root': {
          color: 'white'
        }
      }
    }
  }), [muiTheme.palette])

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: { xs: 2, sm: 3 },
        py: { xs: 1.5, sm: 2 },
        backgroundColor: 'background.paper',
        borderRadius: { xs: 1, sm: 2 },
        boxShadow: 1,
        mb: { xs: 2, sm: 3 },
        width: '100%',
        minHeight: { xs: 56, sm: 64 },
        flexWrap: { xs: 'wrap', md: 'nowrap' },
        gap: { xs: 1, sm: 2 }
      }}
    >
      {/* Sol taraf - Bağlantı durumları */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        gap: { xs: 1, sm: 2 },
        order: { xs: 3, md: 1 },
        width: { xs: '100%', md: 'auto' },
        justifyContent: { xs: 'center', md: 'flex-start' }
      }}>
        {/* İnternet bağlantısı */}
        <Tooltip title={connectionData.internet.text}>
          <Chip
            icon={connectionData.internet.icon}
            label={t('header.internet.label')}
            color={connectionData.internet.color}
            variant="outlined"
            size="small"
            sx={chipStyles}
          />
        </Tooltip>

        {/* Backend bağlantısı */}
        <Tooltip title={connectionData.backend.text}>
          <Chip
            icon={connectionData.backend.icon}
            label={t('header.backend.label')}
            color={connectionData.backend.color}
            variant="outlined"
            size="small"
            sx={chipStyles}
          />
        </Tooltip>
      </Box>

      {/* Orta - Şube adı */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        order: { xs: 1, md: 2 },
        justifyContent: { xs: 'center', md: 'center' },
        flex: { md: 1 }
      }}>
        <Store sx={{
          color: 'text.secondary',
          fontSize: { xs: 20, sm: 24 }
        }} />
        <Typography
          variant="h6"
          color="text.primary"
          fontWeight="medium"
          sx={{
            fontSize: { xs: '1rem', sm: '1.25rem' },
            textAlign: 'center'
          }}
        >
          {branchName}
        </Typography>
      </Box>

      {/* Sağ taraf - Kontroller ve Kullanıcı bilgisi */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        gap: { xs: 0.5, sm: 1 },
        order: { xs: 2, md: 3 },
        justifyContent: { xs: 'flex-end', md: 'flex-end' }
      }}>
        {/* Dil değiştirme butonu */}
        <Tooltip title={t('header.language.tooltip')}>
          <IconButton
            onClick={toggleLanguage}
            size="small"
            sx={iconButtonStyles.language}
          >
            <Typography
              variant="caption"
              fontWeight="bold"
              sx={{ fontSize: { xs: '0.65rem', sm: '0.75rem' } }}
            >
              {currentLanguage}
            </Typography>
          </IconButton>
        </Tooltip>

        {/* Tema değiştirme butonu */}
        <Tooltip title={t('header.theme.tooltip')}>
          <IconButton
            onClick={toggleTheme}
            size="small"
            sx={iconButtonStyles.theme}
          >
            {themeMode === 'light' ? (
              <DarkMode sx={{ fontSize: { xs: 16, sm: 18 } }} />
            ) : (
              <LightMode sx={{ fontSize: { xs: 16, sm: 18 } }} />
            )}
          </IconButton>
        </Tooltip>

        {/* Kullanıcı bilgisi */}
        <Box sx={{
          textAlign: 'right',
          mr: { xs: 0.5, sm: 1 },
          display: { xs: 'none', sm: 'block' }
        }}>
          <Typography
            variant="body2"
            color="text.primary"
            fontWeight="medium"
            sx={{ fontSize: { sm: '0.875rem' } }}
          >
            {user?.username || 'Kullanıcı'}
          </Typography>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ fontSize: { sm: '0.75rem' } }}
          >
            {user?.role || 'Rol'}
          </Typography>
        </Box>

        <Tooltip title={t('header.user.menu')}>
          <IconButton
            onClick={handleUserMenuOpen}
            sx={iconButtonStyles.user}
          >
            <Avatar sx={{
              width: { xs: 28, sm: 32 },
              height: { xs: 28, sm: 32 },
              bgcolor: 'transparent'
            }}>
              <Person sx={{
                color: 'primary.main',
                fontSize: { xs: 18, sm: 20 }
              }} />
            </Avatar>
          </IconButton>
        </Tooltip>

        {/* Kullanıcı menüsü */}
        <Menu
          anchorEl={anchorEl}
          open={isMenuOpen}
          onClose={handleUserMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem disabled>
            <AccountCircle sx={{ mr: 1 }} />
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {user?.username}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {user?.role}
              </Typography>
            </Box>
          </MenuItem>
          
          <Divider />
          
          <MenuItem onClick={handleLogout}>
            <Logout sx={{ mr: 1 }} />
            {t('header.user.logout')}
          </MenuItem>
        </Menu>
      </Box>
    </Box>
  )
}

// ✅ Kural 17: React.memo for performance optimization
export const MemoizedDashboardHeader = React.memo(DashboardHeader, (prevProps, nextProps) => {
  // Custom comparison - only re-render if branchName changes
  return prevProps.branchName === nextProps.branchName
})

// Export both versions
export default DashboardHeader
