// Product Variant Service - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 6: Service Layer Pattern
// ✅ Kural 7: Error Handling Pattern
// ✅ Kural 9: Prisma Transaction Pattern

import { PrismaClient, Prisma } from '@prisma/client'
import {
  ApiResponse,
  PaginatedResponse
} from '@shared/types'
import { 
  CreateVariantInput, 
  UpdateVariantInput, 
  VariantQueryInput 
} from '../validators/variantValidators'
import { AppError, ErrorCodes } from '../utils/AppError'
import { logger } from '../utils/logger'

const prisma = new PrismaClient()

// ✅ ProductVariant type definition
interface ProductVariant {
  id: string
  productId: string
  name: string
  code: string
  sku?: string | null
  barcode?: string | null
  price: number
  costPrice?: number | null
  displayOrder: number
  active: boolean
  version: number
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date | null
  product?: {
    id: string
    name: string
    code: string
    companyId: string
  }
}

// ✅ Utility function to convert Prisma Decimal to number
const transformVariant = (variant: Record<string, unknown>): ProductVariant => {
  return {
    ...variant,
    price: Number(variant.price),
    costPrice: variant.costPrice ? Number(variant.costPrice) : null,
    product: variant.product ? {
      ...(variant.product as Record<string, unknown>)
    } : undefined
  } as ProductVariant
}

// ✅ Kural 6: Service Class Pattern
export class VariantService {
  
  /**
   * ✅ Kural 7: GET /api/products/:productId/variants - Paginated list
   */
  async getVariants(
    companyId: string,
    productId: string,
    query: VariantQueryInput
  ): Promise<PaginatedResponse<ProductVariant>> {
    try {
      logger.info('Fetching product variants', { companyId, productId, query })

      // First verify product exists and belongs to company
      const product = await prisma.product.findFirst({
        where: {
          id: productId,
          companyId,
          deletedAt: null
        }
      })

      if (!product) {
        throw new AppError(
          'Ürün bulunamadı',
          404,
          ErrorCodes.PRODUCT_NOT_FOUND
        )
      }

      const {
        page,
        limit,
        search,
        active,
        sortBy,
        sortOrder
      } = query

      // ✅ Where conditions
      const where: Record<string, unknown> = {
        productId,
        deletedAt: null
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { code: { contains: search, mode: 'insensitive' } },
          { sku: { contains: search, mode: 'insensitive' } },
          { barcode: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (active !== undefined) {
        where.active = active
      }

      // ✅ Order by
      const orderBy: Record<string, string> = { [sortBy]: sortOrder }

      // ✅ Pagination
      const skip = (page - 1) * limit
      const take = limit

      // ✅ Parallel queries for performance
      const [variants, total] = await Promise.all([
        prisma.productVariant.findMany({
          where,
          orderBy,
          skip,
          take,
          include: {
            product: {
              select: {
                id: true,
                name: true,
                code: true,
                companyId: true
              }
            }
          }
        }),
        prisma.productVariant.count({ where })
      ])

      const totalPages = Math.ceil(total / limit)

      logger.info('Product variants fetched successfully', { 
        companyId, 
        productId,
        count: variants.length, 
        total 
      })

      return {
        success: true,
        data: variants.map(transformVariant),
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    } catch (error) {
      if (error instanceof AppError) throw error
      
      logger.error('Failed to fetch product variants', { error, companyId, productId, query })
      throw new AppError(
        'Ürün varyantları getirilemedi',
        500,
        ErrorCodes.INTERNAL_ERROR,
        error
      )
    }
  }

  /**
   * ✅ Kural 9: Transaction pattern ile create
   */
  async createVariant(
    companyId: string,
    productId: string,
    data: CreateVariantInput
  ): Promise<ApiResponse<ProductVariant>> {
    try {
      logger.info('Creating product variant', { companyId, productId, data })

      // ✅ Kural 9: Prisma Transaction Pattern
      const variant = await prisma.$transaction(async (tx) => {
        // 1. Verify product exists and belongs to company
        const product = await tx.product.findFirst({
          where: {
            id: productId,
            companyId,
            deletedAt: null
          }
        })

        if (!product) {
          throw new AppError(
            'Ürün bulunamadı',
            404,
            ErrorCodes.PRODUCT_NOT_FOUND
          )
        }

        // 2. Check unique constraints within product
        await this.checkUniqueConstraints(tx, productId, data.code, data.sku, data.barcode)

        // 3. Create variant
        const newVariant = await tx.productVariant.create({
          data: {
            ...data,
            productId
          },
          include: {
            product: {
              select: {
                id: true,
                name: true,
                code: true,
                companyId: true
              }
            }
          }
        })

        logger.info('Product variant created successfully', { 
          variantId: newVariant.id, 
          code: newVariant.code,
          productId 
        })

        return newVariant
      })

      return {
        success: true,
        data: transformVariant(variant),
        message: 'Ürün varyantı başarıyla oluşturuldu'
      }
    } catch (error) {
      if (error instanceof AppError) throw error

      logger.error('Failed to create product variant', { error, companyId, productId, data })
      throw new AppError(
        'Ürün varyantı oluşturulamadı',
        500,
        ErrorCodes.INTERNAL_ERROR,
        error
      )
    }
  }

  /**
   * ✅ Check unique constraints within product
   */
  private async checkUniqueConstraints(
    tx: Prisma.TransactionClient,
    productId: string,
    code: string,
    sku?: string,
    barcode?: string,
    excludeVariantId?: string
  ): Promise<void> {
    // Check code uniqueness within product
    const existingCode = await tx.productVariant.findFirst({
      where: {
        productId,
        code,
        deletedAt: null,
        ...(excludeVariantId && { id: { not: excludeVariantId } })
      }
    })

    if (existingCode) {
      throw new AppError(
        'Bu ürün için bu kod zaten kullanılıyor',
        400,
        'VARIANT_CODE_EXISTS'
      )
    }

    // Check SKU uniqueness within product (if provided)
    if (sku) {
      const existingSku = await tx.productVariant.findFirst({
        where: {
          productId,
          sku,
          deletedAt: null,
          ...(excludeVariantId && { id: { not: excludeVariantId } })
        }
      })

      if (existingSku) {
        throw new AppError(
          'Bu ürün için bu SKU zaten kullanılıyor',
          400,
          'VARIANT_SKU_EXISTS'
        )
      }
    }

    // Check barcode uniqueness within product (if provided)
    if (barcode) {
      const existingBarcode = await tx.productVariant.findFirst({
        where: {
          productId,
          barcode,
          deletedAt: null,
          ...(excludeVariantId && { id: { not: excludeVariantId } })
        }
      })

      if (existingBarcode) {
        throw new AppError(
          'Bu ürün için bu barkod zaten kullanılıyor',
          400,
          'VARIANT_BARCODE_EXISTS'
        )
      }
    }
  }

  /**
   * ✅ Kural 9: Transaction pattern ile update
   */
  async updateVariant(
    companyId: string,
    variantId: string,
    data: UpdateVariantInput
  ): Promise<ApiResponse<ProductVariant>> {
    try {
      logger.info('Updating product variant', { companyId, variantId, data })

      // ✅ Kural 9: Prisma Transaction Pattern
      const variant = await prisma.$transaction(async (tx) => {
        // 1. Verify variant exists and product belongs to company
        const existingVariant = await tx.productVariant.findFirst({
          where: {
            id: variantId,
            deletedAt: null,
            product: {
              companyId,
              deletedAt: null
            }
          },
          include: {
            product: true
          }
        })

        if (!existingVariant) {
          throw new AppError(
            'Ürün varyantı bulunamadı',
            404,
            'VARIANT_NOT_FOUND'
          )
        }

        // 2. Check unique constraints if code, sku, or barcode is being updated
        if (data.code || data.sku || data.barcode) {
          await this.checkUniqueConstraints(
            tx,
            existingVariant.productId,
            data.code || existingVariant.code,
            data.sku !== undefined ? data.sku : existingVariant.sku,
            data.barcode !== undefined ? data.barcode : existingVariant.barcode,
            variantId
          )
        }

        // 3. Update variant
        const updatedVariant = await tx.productVariant.update({
          where: { id: variantId },
          data: {
            ...data,
            version: { increment: 1 } // Optimistic locking
          },
          include: {
            product: {
              select: {
                id: true,
                name: true,
                code: true,
                companyId: true
              }
            }
          }
        })

        logger.info('Product variant updated successfully', { variantId })
        return updatedVariant
      })

      return {
        success: true,
        data: transformVariant(variant),
        message: 'Ürün varyantı başarıyla güncellendi'
      }
    } catch (error) {
      if (error instanceof AppError) throw error

      logger.error('Failed to update product variant', { error, companyId, variantId, data })
      throw new AppError(
        'Ürün varyantı güncellenemedi',
        500,
        'VARIANT_UPDATE_FAILED',
        error
      )
    }
  }

  /**
   * ✅ Kural 9: Soft delete pattern
   */
  async deleteVariant(
    companyId: string,
    variantId: string
  ): Promise<ApiResponse<null>> {
    try {
      logger.info('Deleting product variant', { companyId, variantId })

      // ✅ Kural 9: Prisma Transaction Pattern
      await prisma.$transaction(async (tx) => {
        // 1. Verify variant exists and product belongs to company
        const existingVariant = await tx.productVariant.findFirst({
          where: {
            id: variantId,
            deletedAt: null,
            product: {
              companyId,
              deletedAt: null
            }
          }
        })

        if (!existingVariant) {
          throw new AppError(
            'Ürün varyantı bulunamadı',
            404,
            'VARIANT_NOT_FOUND'
          )
        }

        // 2. Soft delete variant
        await tx.productVariant.update({
          where: { id: variantId },
          data: {
            deletedAt: new Date(),
            version: { increment: 1 }
          }
        })

        logger.info('Product variant deleted successfully', { variantId })
      })

      return {
        success: true,
        data: null,
        message: 'Ürün varyantı başarıyla silindi'
      }
    } catch (error) {
      if (error instanceof AppError) throw error

      logger.error('Failed to delete product variant', { error, companyId, variantId })
      throw new AppError(
        'Ürün varyantı silinemedi',
        500,
        'VARIANT_DELETE_FAILED',
        error
      )
    }
  }

  /**
   * ✅ Get single variant by ID
   */
  async getVariantById(
    companyId: string,
    variantId: string
  ): Promise<ApiResponse<ProductVariant>> {
    try {
      logger.info('Fetching product variant by ID', { companyId, variantId })

      const variant = await prisma.productVariant.findFirst({
        where: {
          id: variantId,
          deletedAt: null,
          product: {
            companyId,
            deletedAt: null
          }
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              code: true,
              companyId: true
            }
          }
        }
      })

      if (!variant) {
        throw new AppError(
          'Ürün varyantı bulunamadı',
          404,
          'VARIANT_NOT_FOUND'
        )
      }

      logger.info('Product variant fetched successfully', { variantId })

      return {
        success: true,
        data: transformVariant(variant),
        message: 'Ürün varyantı başarıyla getirildi'
      }
    } catch (error) {
      if (error instanceof AppError) throw error

      logger.error('Failed to fetch product variant', { error, companyId, variantId })
      throw new AppError(
        'Ürün varyantı getirilemedi',
        500,
        ErrorCodes.INTERNAL_ERROR,
        error
      )
    }
  }
}

// ✅ Kural 8: Export pattern
export const variantService = new VariantService()
