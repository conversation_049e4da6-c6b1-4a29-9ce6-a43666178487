# VARYANT API TESTLERİ - DEVELOPMENT_RULES.md'ye uygun
# PowerShell Test Script

Write-Host "🚀 VARYANT API TESTLERİ BAŞLIYOR" -ForegroundColor Green
Write-Host "Base URL: http://localhost:3000" -ForegroundColor Yellow

# 1. LOGIN VE TOKEN AL
Write-Host "`n=== 1. LOGIN ===" -ForegroundColor Cyan
$loginData = @{
    username = "admin"
    password = "password"
} | ConvertTo-Json -Compress

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    $token = $loginResponse.data.token
    Write-Host "✅ Login başarılı" -ForegroundColor Green
    Write-Host "Token: $($token.Substring(0,20))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ Login hatası: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Headers hazırla
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# 2. ÜRÜN LİSTESİ AL
Write-Host "`n=== 2. ÜRÜN LİSTESİ ===" -ForegroundColor Cyan
try {
    $products = Invoke-RestMethod -Uri "http://localhost:3000/api/products" -Method GET -Headers $headers
    $productId = $products.data[0].id
    $productName = $products.data[0].name
    Write-Host "✅ Ürün listesi alındı" -ForegroundColor Green
    Write-Host "Test Product ID: $productId" -ForegroundColor Yellow
    Write-Host "Test Product Name: $productName" -ForegroundColor Yellow
} catch {
    Write-Host "❌ Ürün listesi hatası: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. VARYANT LİSTESİ (BOŞ OLMALI)
Write-Host "`n=== 3. VARYANT LİSTESİ (BOŞ) ===" -ForegroundColor Cyan
try {
    $variants = Invoke-RestMethod -Uri "http://localhost:3000/api/products/$productId/variants" -Method GET -Headers $headers
    Write-Host "✅ Varyant listesi alındı" -ForegroundColor Green
    Write-Host "Toplam varyant sayısı: $($variants.pagination.total)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Varyant listesi hatası: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. YENİ VARYANT OLUŞTUR
Write-Host "`n=== 4. YENİ VARYANT OLUŞTUR ===" -ForegroundColor Cyan
$newVariant = @{
    name = "Küçük Boy"
    code = "S"
    sku = "PROD-S"
    barcode = "1234567890123"
    price = 15.50
    costPrice = 8.75
    displayOrder = 1
    active = $true
} | ConvertTo-Json -Compress

try {
    $createResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/products/$productId/variants" -Method POST -Body $newVariant -Headers $headers
    $variantId = $createResponse.data.id
    Write-Host "✅ Varyant oluşturuldu" -ForegroundColor Green
    Write-Host "Variant ID: $variantId" -ForegroundColor Yellow
    Write-Host "Variant Name: $($createResponse.data.name)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Varyant oluşturma hatası: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = [System.IO.StreamReader]::new($stream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Hata detayı: $errorBody" -ForegroundColor Red
    }
}

# 5. İKİNCİ VARYANT OLUŞTUR
Write-Host "`n=== 5. İKİNCİ VARYANT OLUŞTUR ===" -ForegroundColor Cyan
$newVariant2 = @{
    name = "Büyük Boy"
    code = "L"
    sku = "PROD-L"
    barcode = "1234567890124"
    price = 25.50
    costPrice = 12.75
    displayOrder = 2
    active = $true
} | ConvertTo-Json -Compress

try {
    $createResponse2 = Invoke-RestMethod -Uri "http://localhost:3000/api/products/$productId/variants" -Method POST -Body $newVariant2 -Headers $headers
    $variantId2 = $createResponse2.data.id
    Write-Host "✅ İkinci varyant oluşturuldu" -ForegroundColor Green
    Write-Host "Variant ID: $variantId2" -ForegroundColor Yellow
} catch {
    Write-Host "❌ İkinci varyant oluşturma hatası: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. VARYANT LİSTESİ (DOLU OLMALI)
Write-Host "`n=== 6. VARYANT LİSTESİ (DOLU) ===" -ForegroundColor Cyan
try {
    $variants = Invoke-RestMethod -Uri "http://localhost:3000/api/products/$productId/variants" -Method GET -Headers $headers
    Write-Host "✅ Varyant listesi alındı" -ForegroundColor Green
    Write-Host "Toplam varyant sayısı: $($variants.pagination.total)" -ForegroundColor Gray
    foreach ($variant in $variants.data) {
        Write-Host "  - $($variant.name) ($($variant.code)) - ₺$($variant.price)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ Varyant listesi hatası: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. VARYANT DETAYI
if ($variantId) {
    Write-Host "`n=== 7. VARYANT DETAYI ===" -ForegroundColor Cyan
    try {
        $variantDetail = Invoke-RestMethod -Uri "http://localhost:3000/api/variants/$variantId" -Method GET -Headers $headers
        Write-Host "✅ Varyant detayı alındı" -ForegroundColor Green
        Write-Host "Name: $($variantDetail.data.name)" -ForegroundColor Gray
        Write-Host "Price: ₺$($variantDetail.data.price)" -ForegroundColor Gray
    } catch {
        Write-Host "❌ Varyant detayı hatası: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 8. VARYANT GÜNCELLE
if ($variantId) {
    Write-Host "`n=== 8. VARYANT GÜNCELLE ===" -ForegroundColor Cyan
    $updateVariant = @{
        name = "Küçük Boy (Güncellenmiş)"
        price = 17.50
        active = $true
    } | ConvertTo-Json -Compress

    try {
        $updateResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/variants/$variantId" -Method PUT -Body $updateVariant -Headers $headers
        Write-Host "✅ Varyant güncellendi" -ForegroundColor Green
        Write-Host "Yeni name: $($updateResponse.data.name)" -ForegroundColor Gray
        Write-Host "Yeni price: ₺$($updateResponse.data.price)" -ForegroundColor Gray
    } catch {
        Write-Host "❌ Varyant güncelleme hatası: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 9. VALIDATION HATALARI TESTİ
Write-Host "`n=== 9. VALIDATION HATALARI TESTİ ===" -ForegroundColor Cyan

# Geçersiz varyant oluşturma (boş name)
$invalidVariant = @{
    name = ""
    code = "XL"
    price = -5  # Negatif fiyat
} | ConvertTo-Json -Compress

try {
    $invalidResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/products/$productId/variants" -Method POST -Body $invalidVariant -Headers $headers
    Write-Host "❌ Validation hatası beklenmiyordu!" -ForegroundColor Red
} catch {
    Write-Host "✅ Validation hatası doğru şekilde yakalandı" -ForegroundColor Green
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = [System.IO.StreamReader]::new($stream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Validation hatası: $errorBody" -ForegroundColor Gray
    }
}

# 10. VARYANT SİL (SOFT DELETE)
if ($variantId2) {
    Write-Host "`n=== 10. VARYANT SİL (SOFT DELETE) ===" -ForegroundColor Cyan
    try {
        $deleteResponse = Invoke-RestMethod -Uri "http://localhost:3000/api/variants/$variantId2" -Method DELETE -Headers $headers
        Write-Host "✅ Varyant silindi" -ForegroundColor Green
        Write-Host "Message: $($deleteResponse.message)" -ForegroundColor Gray
    } catch {
        Write-Host "❌ Varyant silme hatası: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 11. SİLİNEN VARYANT LİSTESİNDE GÖRÜNMEMELI
Write-Host "`n=== 11. SİLİNEN VARYANT KONTROLÜ ===" -ForegroundColor Cyan
try {
    $variants = Invoke-RestMethod -Uri "http://localhost:3000/api/products/$productId/variants" -Method GET -Headers $headers
    Write-Host "✅ Varyant listesi alındı" -ForegroundColor Green
    Write-Host "Toplam varyant sayısı (silinen hariç): $($variants.pagination.total)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Varyant listesi hatası: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 VARYANT API TESTLERİ TAMAMLANDI!" -ForegroundColor Green
