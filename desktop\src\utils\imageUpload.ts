// Image Upload Utility - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 8: <PERSON><PERSON><PERSON>ling
// ✅ Kural 19: <PERSON><PERSON> sistemi kull<PERSON>

import { AppError } from '../services/productService'

// ✅ Konfigürasyon sabitleri
export const IMAGE_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.webp'],
  QUALITY: 0.8, // %80 kalite
  MAX_WIDTH: 1200,
  MAX_HEIGHT: 1200,
  THUMBNAIL_SIZE: 300,
  STORAGE_PATH: 'images/products'
} as const

// ✅ Resim dosyası validasyonu
export const validateImageFile = (file: File): void => {
  // Dosya boyutu kontrolü
  if (file.size > IMAGE_CONFIG.MAX_FILE_SIZE) {
    throw new AppError(
      `Dosya boyutu çok büyük. Maksimum ${IMAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB olabilir.`,
      400,
      'FILE_TOO_LARGE'
    )
  }

  // Dosya formatı kontrolü
  if (!IMAGE_CONFIG.ALLOWED_FORMATS.includes(file.type as any)) {
    throw new AppError(
      `Desteklenmeyen dosya formatı. Sadece ${IMAGE_CONFIG.ALLOWED_EXTENSIONS.join(', ')} dosyaları yükleyebilirsiniz.`,
      400,
      'INVALID_FILE_FORMAT'
    )
  }
}

// ✅ URL validasyonu
export const validateImageUrl = (url: string): void => {
  try {
    const urlObj = new URL(url)
    
    // Protocol kontrolü
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      throw new AppError(
        'Geçersiz URL protokolü. HTTP veya HTTPS kullanın.',
        400,
        'INVALID_URL_PROTOCOL'
      )
    }

    // Dosya uzantısı kontrolü
    const pathname = urlObj.pathname.toLowerCase()
    const hasValidExtension = IMAGE_CONFIG.ALLOWED_EXTENSIONS.some(ext => 
      pathname.endsWith(ext)
    )

    if (!hasValidExtension) {
      throw new AppError(
        `Geçersiz resim URL'si. URL ${IMAGE_CONFIG.ALLOWED_EXTENSIONS.join(', ')} uzantılı bir dosyaya işaret etmelidir.`,
        400,
        'INVALID_IMAGE_URL'
      )
    }
  } catch (error) {
    if (error instanceof AppError) throw error
    
    throw new AppError(
      'Geçersiz URL formatı.',
      400,
      'INVALID_URL_FORMAT'
    )
  }
}

// ✅ Dosyayı Base64'e dönüştürme
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result)
      } else {
        reject(new AppError('Dosya okunamadı', 500, 'FILE_READ_ERROR'))
      }
    }
    
    reader.onerror = () => {
      reject(new AppError('Dosya okuma hatası', 500, 'FILE_READ_ERROR'))
    }
    
    reader.readAsDataURL(file)
  })
}

// ✅ Resim boyutlandırma ve optimizasyon
export const resizeImage = (
  file: File, 
  maxWidth: number = IMAGE_CONFIG.MAX_WIDTH,
  maxHeight: number = IMAGE_CONFIG.MAX_HEIGHT,
  quality: number = IMAGE_CONFIG.QUALITY
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    if (!ctx) {
      reject(new AppError('Canvas desteklenmiyor', 500, 'CANVAS_NOT_SUPPORTED'))
      return
    }

    img.onload = () => {
      // Orijinal boyutları al
      let { width, height } = img

      // Boyutlandırma hesaplama
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height)
        width *= ratio
        height *= ratio
      }

      // Canvas boyutunu ayarla
      canvas.width = width
      canvas.height = height

      // Resmi çiz
      ctx.drawImage(img, 0, 0, width, height)

      // Base64 olarak dönüştür
      try {
        const base64 = canvas.toDataURL('image/jpeg', quality)
        resolve(base64)
      } catch (error) {
        reject(new AppError('Resim işleme hatası', 500, 'IMAGE_PROCESSING_ERROR'))
      }
    }

    img.onerror = () => {
      reject(new AppError('Resim yüklenemedi', 500, 'IMAGE_LOAD_ERROR'))
    }

    // Dosyayı resim olarak yükle
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target?.result) {
        img.src = e.target.result as string
      }
    }
    reader.readAsDataURL(file)
  })
}

// ✅ Clipboard'dan resim alma
export const getImageFromClipboard = async (): Promise<File | null> => {
  try {
    const clipboardItems = await navigator.clipboard.read()
    
    for (const clipboardItem of clipboardItems) {
      for (const type of clipboardItem.types) {
        if (type.startsWith('image/')) {
          const blob = await clipboardItem.getType(type)
          return new File([blob], `clipboard-image.${type.split('/')[1]}`, { type })
        }
      }
    }
    
    return null
  } catch (error) {
    console.warn('Clipboard erişimi başarısız:', error)
    return null
  }
}

// ✅ Yerel dosya sistemi için path oluşturma
export const generateImagePath = (filename: string): string => {
  const timestamp = Date.now()
  const randomId = Math.random().toString(36).substring(2, 15)
  const extension = filename.split('.').pop()?.toLowerCase() || 'jpg'
  
  return `${IMAGE_CONFIG.STORAGE_PATH}/${timestamp}-${randomId}.${extension}`
}

// ✅ Resim boyutlarını alma
export const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      resolve({ width: img.width, height: img.height })
    }
    
    img.onerror = () => {
      reject(new AppError('Resim boyutları alınamadı', 500, 'IMAGE_DIMENSIONS_ERROR'))
    }
    
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target?.result) {
        img.src = e.target.result as string
      }
    }
    reader.readAsDataURL(file)
  })
}

// ✅ Dosya boyutunu human-readable formata çevirme
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// ✅ Resim preview URL'si oluşturma
export const createImagePreview = (file: File): string => {
  return URL.createObjectURL(file)
}

// ✅ Preview URL'sini temizleme
export const revokeImagePreview = (url: string): void => {
  URL.revokeObjectURL(url)
}
