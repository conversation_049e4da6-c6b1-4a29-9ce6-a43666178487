# Restoran POS Projesi Ku<PERSON>i (NPM)

## 1. <PERSON><PERSON>

### 1.1. Teknoloji <PERSON>ack'i

```bash
# Önerilen teknolojiler
- Frontend: React + TypeScript + Vite
- UI Library: Material UI (MUI)
- State Management: Zustand veya Redux Toolkit
- Electron: electron-vite
- Backend: Node.js + Express/Fastify
- ORM: Prisma
- Database: PostgreSQL
- Gerçek zamanlı: Socket.io
```

### 1.2. <PERSON>je <PERSON>

```
restoran-pos/
├── desktop/           # Electron + React uygulaması
├── server/           # Backend API
├── shared/           # Paylaşılan tipler ve utils
├── prisma/
│   └── schema.prisma
├── docker-compose.yml
├── .env.example
├── .gitignore
└── package.json
```

### 1.3. <PERSON><PERSON>

```bash
# Proje klasörü oluştur
mkdir restoran-pos && cd restoran-pos

# Git başlat
git init

# Ana package.json o<PERSON>
npm init -y

# .gitignore oluştur
cat > .gitignore << EOL
node_modules/
dist/
build/
.DS_Store
*.log
.idea/
.vscode/
*.db
*.db-journal
EOL
```

### 1.4. Alt Projeleri Oluştur

```bash
# Klasör yapısını oluştur
mkdir -p desktop server shared prisma

# Desktop (Electron + React)
cd desktop
npm init -y
npm install --save-dev electron electron-builder vite @vitejs/plugin-react @types/react @types/react-dom typescript
npm install react react-dom react-router-dom @tanstack/react-query axios
npm install @mui/material @emotion/react @emotion/styled @mui/icons-material @mui/x-data-grid
npm install dayjs zustand react-hook-form @hookform/resolvers zod
npm install i18next react-i18next

# Server
cd ../server
npm init -y
npm install express cors helmet compression dotenv bcryptjs jsonwebtoken socket.io express-rate-limit
npm install --save-dev @types/node @types/express @types/cors @types/compression @types/bcryptjs @types/jsonwebtoken typescript tsx nodemon

# Shared
cd ../shared
npm init -y
npm install --save-dev typescript

# Root'a dön ve Prisma kur
cd ..
npm install --save-dev prisma
npm install @prisma/client
```

## 2. Environment Yapılandırması

### .env.example

```bash
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/restoran_pos"

# Server
PORT=3000
CLIENT_URL="http://localhost:5173"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRE="7d"
SESSION_SECRET="your-session-secret-change-this"

# E-Archive
EARCHIVE_URL=""
EARCHIVE_USERNAME=""
EARCHIVE_PASSWORD=""

# SMS Provider
SMS_PROVIDER="netgsm"
SMS_API_KEY=""
SMS_API_SECRET=""
SMS_SENDER=""

# Email
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM=""

# File Storage
UPLOAD_PATH="./uploads"
MAX_FILE_SIZE="10485760"

# Redis (optional)
REDIS_URL="redis://localhost:6379"

# Development
NODE_ENV="development"
LOG_LEVEL="debug"
```

### docker-compose.yml

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: pos_postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: restoran_pos
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: pos_redis
    ports:
      - "6379:6379"
    restart: unless-stopped

  adminer:
    image: adminer
    container_name: pos_adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
```

## 3. Database Kurulumu

```bash
# Docker'ı başlat
docker-compose up -d

# Prisma'yı başlat
npx prisma init

# schema.prisma dosyasını kopyala (daha önce hazırladığınız)

# Migration oluştur
npx prisma migrate dev --name init

# Prisma Client'ı generate et
npx prisma generate

# Seed data (opsiyonel)
# prisma/seed.ts oluştur ve package.json'a ekle:
# "prisma": { "seed": "tsx prisma/seed.ts" }
```

## 4. Desktop App Yapılandırması

### desktop/package.json güncelle:

```json
{
  "name": "restoran-pos-desktop",
  "version": "1.0.0",
  "main": "electron/main.js",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "electron": "electron .",
    "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"",
    "dist": "npm run build && electron-builder",
    "dist:win": "npm run build && electron-builder --win",
    "dist:mac": "npm run build && electron-builder --mac",
    "dist:linux": "npm run build && electron-builder --linux"
  },
  "build": {
    "appId": "com.yourcompany.restoran-pos",
    "productName": "Restoran POS",
    "directories": {
      "output": "dist-electron"
    },
    "files": [
      "dist/**/*",
      "electron/**/*",
      "node_modules/**/*"
    ],
    "win": {
      "target": "nsis",
      "icon": "assets/icon.ico"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "language": "1055"
    }
  }
}
```

### desktop/vite.config.ts:

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  base: './',
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    emptyOutDir: true,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@shared': path.resolve(__dirname, '../shared'),
    },
  },
  server: {
    port: 5173,
    strictPort: true,
  },
})
```

### desktop/tsconfig.json:

```json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "paths": {
      "@/*": ["./src/*"],
      "@shared/*": ["../shared/*"]
    }
  },
  "include": ["src", "../shared"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### desktop/electron/main.js:

```javascript
const { app, BrowserWindow, ipcMain, Menu } = require('electron')
const path = require('path')

let mainWindow = null
const isDev = process.env.NODE_ENV === 'development'

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1920,
    height: 1080,
    minWidth: 1024,
    minHeight: 768,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    titleBarStyle: 'default',
    autoHideMenuBar: false,
  })

  // Development
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173')
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // Menu
  const template = [
    {
      label: 'Dosya',
      submenu: [
        { label: 'Yeni Sipariş', accelerator: 'Ctrl+N' },
        { type: 'separator' },
        { label: 'Çıkış', role: 'quit' }
      ]
    },
    {
      label: 'Düzen',
      submenu: [
        { role: 'undo', label: 'Geri Al' },
        { role: 'redo', label: 'İleri Al' },
        { type: 'separator' },
        { role: 'cut', label: 'Kes' },
        { role: 'copy', label: 'Kopyala' },
        { role: 'paste', label: 'Yapıştır' }
      ]
    },
    {
      label: 'Görünüm',
      submenu: [
        { role: 'reload', label: 'Yenile' },
        { role: 'togglefullscreen', label: 'Tam Ekran' },
        { role: 'toggleDevTools', label: 'Geliştirici Araçları' }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

app.whenReady().then(createWindow)

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// IPC handlers
ipcMain.handle('app:version', () => {
  return app.getVersion()
})
```

### desktop/electron/preload.js:

```javascript
const { contextBridge, ipcRenderer } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
  getVersion: () => ipcRenderer.invoke('app:version'),
  
  // Printer API
  getPrinters: () => ipcRenderer.invoke('get-printers'),
  print: (data) => ipcRenderer.invoke('print', data),
  
  // File API
  selectFile: () => ipcRenderer.invoke('select-file'),
  saveFile: (data) => ipcRenderer.invoke('save-file', data),
  
  // System
  minimize: () => ipcRenderer.send('minimize'),
  maximize: () => ipcRenderer.send('maximize'),
  close: () => ipcRenderer.send('close'),
})
```

## 5. Server Yapılandırması

### server/package.json güncelle:

```json
{
  "name": "restoran-pos-server",
  "version": "1.0.0",
  "main": "dist/index.js",
  "scripts": {
    "dev": "nodemon --exec tsx src/index.ts",
    "build": "tsc",
    "start": "node dist/index.js",
    "lint": "eslint src --ext .ts",
    "test": "jest",
    "migrate": "prisma migrate dev",
    "generate": "prisma generate",
    "studio": "prisma studio",
    "seed": "tsx prisma/seed.ts"
  }
}
```

### server/tsconfig.json:

```json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "commonjs",
    "lib": ["ES2022"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "moduleResolution": "node",
    "allowJs": false,
    "noEmit": false,
    "incremental": true,
    "sourceMap": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "paths": {
      "@/*": ["./src/*"],
      "@shared/*": ["../shared/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "**/*.test.ts"]
}
```

### server/nodemon.json:

```json
{
  "watch": ["src", "../prisma/schema.prisma"],
  "ext": "ts,js,json",
  "ignore": ["src/**/*.test.ts"],
  "exec": "tsx",
  "env": {
    "NODE_ENV": "development"
  }
}
```

## 6. Shared Types Yapılandırması

### shared/tsconfig.json:

```json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "lib": ["ES2022"],
    "declaration": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

## 7. Root Package.json Scripts

```json
{
  "name": "restoran-pos",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "concurrently \"npm run dev:server\" \"npm run dev:desktop\"",
    "dev:server": "cd server && npm run dev",
    "dev:desktop": "cd desktop && npm run electron:dev",
    "build": "npm run build:server && npm run build:desktop",
    "build:server": "cd server && npm run build",
    "build:desktop": "cd desktop && npm run build",
    "dist": "cd desktop && npm run dist",
    "dist:win": "cd desktop && npm run dist:win",
    "db:migrate": "prisma migrate dev",
    "db:generate": "prisma generate",
    "db:studio": "prisma studio",
    "db:seed": "cd server && npm run seed",
    "docker:up": "docker-compose up -d",
    "docker:down": "docker-compose down",
    "docker:logs": "docker-compose logs -f",
    "clean": "rm -rf node_modules desktop/node_modules server/node_modules shared/node_modules",
    "install:all": "npm install && cd desktop && npm install && cd ../server && npm install && cd ../shared && npm install",
    "lint": "cd server && npm run lint",
    "test": "cd server && npm test"
  },
  "devDependencies": {
    "concurrently": "^8.2.2",
    "wait-on": "^7.2.0"
  }
}
```

## 8. Projeyi Başlatma

```bash
# Root'ta concurrently ve wait-on kur
npm install --save-dev concurrently wait-on

# Tüm bağımlılıkları kur
npm run install:all

# Docker'ı başlat
npm run docker:up

# Database migration
npm run db:migrate

# Development modda başlat
npm run dev
```

## 9. Klasör Yapısı Detayı

```
restoran-pos/
├── desktop/
│   ├── electron/
│   │   ├── main.js
│   │   └── preload.js
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── layouts/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── store/
│   │   ├── utils/
│   │   ├── i18n/
│   │   ├── App.tsx
│   │   └── main.tsx
│   ├── assets/
│   ├── index.html
│   ├── vite.config.ts
│   ├── tsconfig.json
│   └── package.json
├── server/
│   ├── src/
│   │   ├── controllers/
│   │   ├── routes/
│   │   ├── middlewares/
│   │   ├── services/
│   │   ├── utils/
│   │   ├── validators/
│   │   ├── types/
│   │   └── index.ts
│   ├── tsconfig.json
│   ├── nodemon.json
│   └── package.json
├── shared/
│   ├── src/
│   │   ├── types/
│   │   ├── enums/
│   │   ├── constants/
│   │   └── index.ts
│   ├── tsconfig.json
│   └── package.json
├── prisma/
│   ├── schema.prisma
│   ├── seed.ts
│   └── migrations/
├── docker-compose.yml
├── .env.example
├── .gitignore
└── package.json
```

## 10. VS Code Ayarları

### .vscode/settings.json:

```json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true
  }
}
```

### .vscode/launch.json:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Server",
      "type": "node",
      "request": "launch",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["run", "dev:server"],
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal"
    },
    {
      "name": "Debug Electron",
      "type": "node",
      "request": "launch",
      "runtimeExecutable": "${workspaceFolder}/desktop/node_modules/.bin/electron",
      "args": ["${workspaceFolder}/desktop"],
      "cwd": "${workspaceFolder}/desktop",
      "console": "integratedTerminal"
    }
  ]
}
```

Bu yapı ile npm kullanarak monorepo benzeri bir yapı kurduk. Her alt proje kendi node_modules'ına sahip ama root'tan yönetilebilir durumda.