// Component Structure Pattern - DEVELOPMENT_RULES.md Kural 14
// ✅ Standard component template following all rules

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Alert
} from '@mui/material'
import { Save as SaveIcon } from '@mui/icons-material'

// ✅ Kural 13: TypeScript interface
interface ComponentTemplateProps {
  // Required props
  id: string
  title: string
  
  // Optional props
  subtitle?: string
  loading?: boolean
  disabled?: boolean
  variant?: 'primary' | 'secondary'
  
  // Event handlers
  onSave?: (data: any) => void | Promise<void>
  onCancel?: () => void
  
  // Children
  children?: React.ReactNode
}

// ✅ Kural 14: Standard component structure
export const ComponentTemplate: React.FC<ComponentTemplateProps> = ({
  id,
  title,
  subtitle,
  loading = false,
  disabled = false,
  variant = 'primary',
  onSave,
  onCancel,
  children
}) => {
  // ========== 1. HOOKS ==========
  // ✅ i18n hook
  const { t } = useTranslation()
  
  // ✅ State hooks
  const [internalLoading, setInternalLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [data, setData] = useState<any>(null)
  
  // ========== 2. COMPUTED VALUES ==========
  // ✅ Kural 17: useMemo for expensive calculations
  const isLoading = useMemo(() => {
    return loading || internalLoading
  }, [loading, internalLoading])
  
  const isDisabled = useMemo(() => {
    return disabled || isLoading
  }, [disabled, isLoading])
  
  // ✅ Kural 17: useMemo for complex objects
  const buttonProps = useMemo(() => ({
    variant: variant === 'primary' ? 'contained' : 'outlined',
    color: variant === 'primary' ? 'primary' : 'secondary',
    disabled: isDisabled,
    startIcon: isLoading ? <CircularProgress size={16} /> : <SaveIcon />
  }), [variant, isDisabled, isLoading])
  
  // ========== 3. EVENT HANDLERS ==========
  // ✅ Kural 17: useCallback for event handlers
  const handleSave = useCallback(async () => {
    if (!onSave || isDisabled) return
    
    try {
      setInternalLoading(true)
      setError(null)
      
      await onSave(data)
      
      // Success feedback could be added here
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : t('common.error.unknown')
      setError(errorMessage)
    } finally {
      setInternalLoading(false)
    }
  }, [onSave, data, isDisabled, t])
  
  const handleCancel = useCallback(() => {
    if (onCancel && !isLoading) {
      onCancel()
    }
  }, [onCancel, isLoading])
  
  const handleDataChange = useCallback((newData: any) => {
    setData(newData)
    // Clear error when data changes
    if (error) {
      setError(null)
    }
  }, [error])
  
  // ========== 4. EFFECTS ==========
  // ✅ Effect for initialization
  useEffect(() => {
    // Component initialization logic
    console.log(`Component ${id} initialized`)
    
    // Cleanup function
    return () => {
      console.log(`Component ${id} cleanup`)
    }
  }, [id])
  
  // ✅ Effect for data changes
  useEffect(() => {
    if (data) {
      console.log(`Data changed for component ${id}:`, data)
    }
  }, [data, id])
  
  // ========== 5. RENDER HELPERS ==========
  // ✅ Conditional rendering helpers
  const renderError = () => {
    if (!error) return null
    
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    )
  }
  
  const renderActions = () => (
    <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
      {onCancel && (
        <Button
          variant="outlined"
          onClick={handleCancel}
          disabled={isLoading}
        >
          {t('common.cancel')}
        </Button>
      )}
      
      {onSave && (
        <Button
          {...buttonProps}
          onClick={handleSave}
        >
          {isLoading ? t('common.saving') : t('common.save')}
        </Button>
      )}
    </Box>
  )
  
  // ========== 6. MAIN RENDER ==========
  return (
    <Box
      id={id}
      sx={{
        p: 3,
        border: 1,
        borderColor: 'divider',
        borderRadius: 2,
        backgroundColor: 'background.paper'
      }}
    >
      {/* Header */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" component="h2">
          {title}
        </Typography>
        
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </Box>
      
      {/* Error display */}
      {renderError()}
      
      {/* Content */}
      <Box sx={{ mb: 2 }}>
        {children || (
          <Typography color="text.secondary">
            {t('common.noContent')}
          </Typography>
        )}
      </Box>
      
      {/* Actions */}
      {renderActions()}
    </Box>
  )
}

// ✅ Kural 17: React.memo for performance optimization
export const MemoizedComponentTemplate = React.memo(ComponentTemplate, (prevProps, nextProps) => {
  // Custom comparison logic
  return (
    prevProps.id === nextProps.id &&
    prevProps.title === nextProps.title &&
    prevProps.loading === nextProps.loading &&
    prevProps.disabled === nextProps.disabled
  )
})

// ✅ Default export
export default ComponentTemplate

// ✅ Named exports for flexibility
export type { ComponentTemplateProps }
