// Variant List Component - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n Usage
// ✅ Kural 19: Material-UI Semantic Colors

import React, { useState, useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  useMediaQuery,
  Tooltip,
  Stack
} from '@mui/material'
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'
import { useVariantStore } from '../../store/useVariantStore'
import { ProductVariant } from '../../types/variantTypes'
import { formatCurrency } from '../../types/variantTypes'

// ✅ Kural 14: Component props interface
interface VariantListProps {
  productId: string
  onEditVariant: (variant: ProductVariant) => void
  onDeleteSuccess: (message: string) => void
  onDeleteError: (message: string) => void
}

// ✅ Kural 14: Standard component structure
export const VariantList: React.FC<VariantListProps> = ({
  productId,
  onEditVariant,
  onDeleteSuccess,
  onDeleteError
}) => {
  // ========== 1. HOOKS ==========
  // ✅ i18n hook
  const { t } = useTranslation()
  
  // ✅ Theme hook
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  // ✅ Store hooks
  const {
    variants,
    loading,
    error,
    pagination,
    filters,
    currentVariant,
    isDeleteDialogOpen,
    fetchVariants,
    deleteVariant,
    setFilters,
    openCreateModal,
    openDeleteDialog,
    closeDeleteDialog,
    refreshVariants
  } = useVariantStore()
  
  // ========== 2. LOCAL STATE ==========
  const [deleteLoading, setDeleteLoading] = useState(false)
  
  // ========== 3. EVENT HANDLERS ==========
  
  /**
   * ✅ Handle page change
   */
  const handlePageChange = useCallback((_: unknown, newPage: number) => {
    setFilters({ page: newPage + 1 }) // MUI uses 0-based, our API uses 1-based
    fetchVariants(productId, { page: newPage + 1 })
  }, [setFilters, fetchVariants, productId])
  
  /**
   * ✅ Handle rows per page change
   */
  const handleRowsPerPageChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newLimit = parseInt(event.target.value, 10)
    setFilters({ limit: newLimit, page: 1 })
    fetchVariants(productId, { limit: newLimit, page: 1 })
  }, [setFilters, fetchVariants, productId])
  
  /**
   * ✅ Handle delete click
   */
  const handleDeleteClick = useCallback((variant: ProductVariant) => {
    openDeleteDialog(variant)
  }, [openDeleteDialog])
  
  /**
   * ✅ Handle delete confirm
   */
  const handleDeleteConfirm = useCallback(async () => {
    if (!currentVariant) return
    
    setDeleteLoading(true)
    try {
      await deleteVariant(currentVariant.id)
      onDeleteSuccess(t('variants.messages.deleteSuccess'))
    } catch (error) {
      onDeleteError(t('variants.messages.deleteError'))
    } finally {
      setDeleteLoading(false)
    }
  }, [currentVariant, deleteVariant, onDeleteSuccess, onDeleteError, t])
  
  /**
   * ✅ Handle refresh
   */
  const handleRefresh = useCallback(() => {
    refreshVariants()
  }, [refreshVariants])
  
  /**
   * ✅ Handle add variant
   */
  const handleAddVariant = useCallback(() => {
    openCreateModal()
  }, [openCreateModal])
  
  // ========== 4. COMPUTED VALUES ==========
  
  /**
   * ✅ Check if list is empty
   */
  const isEmpty = useMemo(() => {
    return !loading && !error && variants.length === 0
  }, [loading, error, variants.length])
  
  // ========== 5. RENDER HELPERS ==========
  
  /**
   * ✅ Render variant status
   */
  const renderVariantStatus = useCallback((variant: ProductVariant) => {
    if (variant.active) {
      return (
        <Chip
          label={t('variants.active')}
          color="success"
          size="small"
        />
      )
    }
    
    return (
      <Chip
        label={t('variants.inactive')}
        color="error"
        size="small"
      />
    )
  }, [t])
  
  /**
   * ✅ Render variant price
   */
  const renderVariantPrice = useCallback((variant: ProductVariant) => {
    return (
      <Typography variant="body2" fontWeight="medium">
        {formatCurrency(variant.price)}
      </Typography>
    )
  }, [])
  
  /**
   * ✅ Render cost price
   */
  const renderCostPrice = useCallback((variant: ProductVariant) => {
    if (!variant.costPrice) {
      return (
        <Typography variant="body2" color="text.secondary">
          -
        </Typography>
      )
    }
    
    return (
      <Typography variant="body2" color="text.secondary">
        {formatCurrency(variant.costPrice)}
      </Typography>
    )
  }, [])
  
  /**
   * ✅ Render action buttons
   */
  const renderActions = useCallback((variant: ProductVariant) => (
    <Box sx={{ display: 'flex', gap: 0.5 }}>
      <Tooltip title={t('variants.actions.edit')}>
        <IconButton
          size="small"
          onClick={() => onEditVariant(variant)}
          color="primary"
        >
          <EditIcon fontSize="small" />
        </IconButton>
      </Tooltip>
      
      <Tooltip title={t('variants.actions.delete')}>
        <IconButton
          size="small"
          onClick={() => handleDeleteClick(variant)}
          color="error"
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      </Tooltip>
    </Box>
  ), [t, onEditVariant, handleDeleteClick])
  
  /**
   * ✅ Render delete dialog
   */
  const renderDeleteDialog = useCallback(() => (
    <Dialog
      open={isDeleteDialogOpen}
      onClose={closeDeleteDialog}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        {t('variants.form.delete')}
      </DialogTitle>
      
      <DialogContent>
        <Typography>
          {t('variants.messages.deleteConfirm')}
        </Typography>
        
        {currentVariant && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            <strong>{currentVariant.name}</strong> ({currentVariant.code})
          </Typography>
        )}
        
        <Typography variant="body2" color="warning.main" sx={{ mt: 2 }}>
          {t('variants.messages.deleteWarning')}
        </Typography>
      </DialogContent>
      
      <DialogActions>
        <Button 
          onClick={closeDeleteDialog}
          disabled={deleteLoading}
        >
          {t('variants.form.cancel')}
        </Button>
        
        <Button
          onClick={handleDeleteConfirm}
          color="error"
          variant="contained"
          disabled={deleteLoading}
          startIcon={deleteLoading ? <CircularProgress size={16} /> : undefined}
        >
          {deleteLoading ? t('variants.form.deleting') : t('variants.form.delete')}
        </Button>
      </DialogActions>
    </Dialog>
  ), [
    isDeleteDialogOpen,
    closeDeleteDialog,
    currentVariant,
    deleteLoading,
    handleDeleteConfirm,
    t
  ])
  
  /**
   * ✅ Render header
   */
  const renderHeader = useCallback(() => (
    <Box sx={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      mb: 2 
    }}>
      <Typography variant="h6" component="h2">
        {t('variants.list.title')}
      </Typography>
      
      <Stack direction="row" spacing={1}>
        <Tooltip title={t('common.refresh')}>
          <IconButton
            onClick={handleRefresh}
            disabled={loading}
            color="primary"
          >
            <RefreshIcon />
          </IconButton>
        </Tooltip>
        
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddVariant}
          color="primary"
        >
          {t('variants.actions.add')}
        </Button>
      </Stack>
    </Box>
  ), [t, handleRefresh, handleAddVariant, loading])
  
  // ========== 6. MAIN RENDER ==========
  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2 }}>
        {renderHeader()}
      </Box>
      
      {/* Loading State */}
      {loading && (
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          flex: 1,
          minHeight: 200
        }}>
          <CircularProgress />
        </Box>
      )}
      
      {/* Error State */}
      {error && (
        <Box sx={{ p: 2 }}>
          <Alert 
            severity="error" 
            action={
              <Button color="inherit" size="small" onClick={handleRefresh}>
                {t('variants.list.retry')}
              </Button>
            }
          >
            {error}
          </Alert>
        </Box>
      )}
      
      {/* Empty State */}
      {isEmpty && (
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column',
          justifyContent: 'center', 
          alignItems: 'center',
          flex: 1,
          minHeight: 200,
          textAlign: 'center',
          p: 3
        }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            {t('variants.list.noVariants')}
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {t('variants.list.addFirstVariant')}
          </Typography>
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddVariant}
            color="primary"
          >
            {t('variants.actions.add')}
          </Button>
        </Box>
      )}
      
      {/* Table Content */}
      {!loading && !error && !isEmpty && (
        <>
          <TableContainer sx={{ flex: 1 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>{t('variants.table.columns.name')}</TableCell>
                  <TableCell>{t('variants.table.columns.code')}</TableCell>
                  <TableCell>{t('variants.table.columns.sku')}</TableCell>
                  <TableCell>{t('variants.table.columns.barcode')}</TableCell>
                  <TableCell>{t('variants.table.columns.price')}</TableCell>
                  <TableCell>{t('variants.table.columns.costPrice')}</TableCell>
                  <TableCell>{t('variants.table.columns.status')}</TableCell>
                  <TableCell align="center">{t('variants.table.columns.actions')}</TableCell>
                </TableRow>
              </TableHead>
              
              <TableBody>
                {variants.map((variant) => (
                  <TableRow key={variant.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {variant.name}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {variant.code}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {variant.sku || '-'}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {variant.barcode || '-'}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      {renderVariantPrice(variant)}
                    </TableCell>
                    
                    <TableCell>
                      {renderCostPrice(variant)}
                    </TableCell>
                    
                    <TableCell>
                      {renderVariantStatus(variant)}
                    </TableCell>
                    
                    <TableCell align="center">
                      {renderActions(variant)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          {/* Pagination */}
          <TablePagination
            component="div"
            count={pagination.total}
            page={pagination.page - 1} // MUI uses 0-based, our API uses 1-based
            onPageChange={handlePageChange}
            rowsPerPage={pagination.limit}
            onRowsPerPageChange={handleRowsPerPageChange}
            rowsPerPageOptions={[10, 20, 50]}
            labelRowsPerPage={t('products.pagination.rowsPerPage')}
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} ${t('products.pagination.of')} ${count}`
            }
          />
        </>
      )}
      
      {/* Delete Confirmation Dialog */}
      {renderDeleteDialog()}
    </Card>
  )
}
