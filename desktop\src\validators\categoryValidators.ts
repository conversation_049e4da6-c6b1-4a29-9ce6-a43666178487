// Category Validators - DEVELOPMENT_RULES.md Kural 13'e uygun
// ✅ Zod Schema Pattern kullanımı

import { z } from 'zod'

// ✅ Kural 13: Zod Schema Pattern

// Create Category Schema
export const createCategorySchema = z.object({
  parentId: z.string().cuid('Geçersiz üst kategori ID').optional(),
  name: z.string()
    .min(1, 'Kategori adı gerekli')
    .max(100, 'Kategori adı maksimum 100 karakter olabilir'),
  description: z.string()
    .max(500, 'Açıklama maksimum 500 karakter olabilir')
    .optional(),
  image: z.string()
    .max(2000, 'Resim URL çok uzun')
    .optional(),
  color: z.string()
    .refine((val) => !val || /^#[0-9A-Fa-f]{6}$/.test(val), 'Geçersiz renk kodu (örn: #FF0000)')
    .optional(),
  icon: z.string()
    .max(50, 'İkon adı maksimum 50 karakter olabilir')
    .optional(),
  showInKitchen: z.boolean().default(true),
  preparationTime: z.number()
    .int('Hazırlık süresi tam sayı olmalı')
    .min(0, 'Hazırlık süresi 0 veya pozitif olmalı')
    .max(1440, 'Hazırlık süresi maksimum 24 saat olabilir')
    .optional(),
  displayOrder: z.number()
    .int('Sıralama tam sayı olmalı')
    .min(0, 'Sıralama 0 veya pozitif olmalı')
    .default(0),
  showInMenu: z.boolean().default(true)
})

// Update Category Schema - Tüm alanlar optional
export const updateCategorySchema = z.object({
  parentId: z.string().cuid('Geçersiz üst kategori ID').optional(),
  name: z.string()
    .min(1, 'Kategori adı gerekli')
    .max(100, 'Kategori adı maksimum 100 karakter olabilir')
    .optional(),
  description: z.string()
    .max(500, 'Açıklama maksimum 500 karakter olabilir')
    .optional(),
  image: z.string()
    .max(2000, 'Resim URL çok uzun')
    .optional(),
  color: z.string()
    .refine((val) => !val || /^#[0-9A-Fa-f]{6}$/.test(val), 'Geçersiz renk kodu (örn: #FF0000)')
    .optional(),
  icon: z.string()
    .max(50, 'İkon adı maksimum 50 karakter olabilir')
    .optional(),
  showInKitchen: z.boolean().optional(),
  preparationTime: z.number()
    .int('Hazırlık süresi tam sayı olmalı')
    .min(0, 'Hazırlık süresi 0 veya pozitif olmalı')
    .max(1440, 'Hazırlık süresi maksimum 24 saat olabilir')
    .optional(),
  displayOrder: z.number()
    .int('Sıralama tam sayı olmalı')
    .min(0, 'Sıralama 0 veya pozitif olmalı')
    .optional(),
  showInMenu: z.boolean().optional(),
  active: z.boolean().optional()
})

// ✅ Kural 13: Type inference
export type CreateCategoryInput = z.infer<typeof createCategorySchema>
export type UpdateCategoryInput = z.infer<typeof updateCategorySchema>

// ✅ Validation helper functions
export const validateCreateCategory = (data: unknown): CreateCategoryInput => {
  return createCategorySchema.parse(data)
}

export const validateUpdateCategory = (data: unknown): UpdateCategoryInput => {
  return updateCategorySchema.parse(data)
}

// ✅ Safe validation functions that return errors
export const safeValidateCreateCategory = (data: unknown): {
  success: boolean
  data?: CreateCategoryInput
  errors?: z.ZodError
} => {
  const result = createCategorySchema.safeParse(data)
  if (result.success) {
    return { success: true, data: result.data }
  }
  return { success: false, errors: result.error }
}

export const safeValidateUpdateCategory = (data: unknown): {
  success: boolean
  data?: UpdateCategoryInput
  errors?: z.ZodError
} => {
  const result = updateCategorySchema.safeParse(data)
  if (result.success) {
    return { success: true, data: result.data }
  }
  return { success: false, errors: result.error }
}

// ✅ Error formatting helper
export const formatValidationErrors = (error: z.ZodError): Record<string, string> => {
  const errors: Record<string, string> = {}
  
  error.issues.forEach((issue) => {
    const path = issue.path.join('.')
    errors[path] = issue.message
  })
  
  return errors
}
