import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import cookieParser from 'cookie-parser'
import dotenv from 'dotenv'
import { createServer } from 'http'
import authRoutes from './routes/authRoutes'
import productRoutes from './routes/productRoutes'
import { AppError } from './utils/AppError'
import { logger } from './utils/logger'
import { initializeSocketService } from './services/socketService'

// Load environment variables
dotenv.config()

const app = express()
const server = createServer(app)
const PORT = process.env.PORT || 3000

// Middleware
app.use(helmet())
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:5173',
  credentials: true
}))
app.use(compression())
app.use(cookieParser())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Restoran POS Server is running!',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  })
})

// ✅ API routes
app.use('/api/auth', authRoutes)
app.use('/api/products', productRoutes)

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API is working!',
    environment: process.env.NODE_ENV || 'development'
  })
})

// ✅ Kural 11: Enhanced Error handling middleware
app.use((err: Error | AppError, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  // Log error
  logger.error('Request error', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  })

  // Handle AppError (our custom errors)
  if (err instanceof AppError) {
    const response: Record<string, unknown> = {
      success: false,
      error: err.message,
      code: err.code
    }

    if (err.details) {
      response.details = err.details
    }

    return res.status(err.statusCode).json(response)
  }

  // Handle other errors
  const isDevelopment = process.env.NODE_ENV === 'development'
  res.status(500).json({
    success: false,
    error: isDevelopment ? err.message : 'Sunucu hatası',
    ...(isDevelopment && { stack: err.stack })
  })
})

// ✅ 404 handler
app.use((req, res) => {
  logger.warn('Route not found', {
    url: req.url,
    method: req.method,
    ip: req.ip
  })

  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.url
  })
})

// ✅ Initialize Socket.io service
const socketService = initializeSocketService(server)

server.listen(PORT, () => {
  const message = `🚀 Restoran POS Server started successfully`
  const details = {
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
    healthCheck: `http://localhost:${PORT}/health`,
    apiDocs: `http://localhost:${PORT}/api`,
    socketConnections: socketService.getConnectedUsersCount(),
    timestamp: new Date().toISOString()
  }

  console.log(message)
  console.log(`📊 Environment: ${details.environment}`)
  console.log(`🌐 Health check: ${details.healthCheck}`)
  console.log(`📦 Products API: http://localhost:${PORT}/api/products`)
  console.log(`🔌 Socket.io initialized and ready`)

  logger.info(message, details)
})
