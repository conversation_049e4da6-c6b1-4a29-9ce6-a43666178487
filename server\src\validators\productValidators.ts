// Product Validators - DEVELOPMENT_RULES.md Kural 13'e uygun
// ✅ Zod Schema Pattern kullanımı

import { z } from 'zod'
import { Request, Response, NextFunction } from 'express'

// ✅ Kural 13: Zod Schema Pattern
const productUnitSchema = z.enum([
  'PIECE', 'KG', 'GRAM', 'LITER', 'ML', 'PORTION', 'BOX', 'PACKAGE'
])

// Create Product Schema
export const createProductSchema = z.object({
  categoryId: z.string().min(1, 'Kategori ID gerekli'),
  code: z.string()
    .min(1, 'Ürün kodu gerekli')
    .max(50, 'Ürün kodu maksimum 50 karakter olabilir')
    .regex(/^[A-Z0-9_-]+$/, 'Ürün kodu sadece büyük harf, rakam, tire ve alt çizgi içerebilir'),
  barcode: z.string()
    .max(50, 'Barkod maksimum 50 karakter olabilir')
    .optional(),
  name: z.string()
    .min(1, '<PERSON>rün adı gerekli')
    .max(200, 'Ürün adı maksimum 200 karakter olabilir'),
  description: z.string()
    .max(1000, 'Açıklama maksimum 1000 karakter olabilir')
    .optional(),
  shortDescription: z.string()
    .max(200, 'Kısa açıklama maksimum 200 karakter olabilir')
    .optional(),
  image: z.string().url('Geçersiz resim URL').optional(),
  images: z.array(z.string().url('Geçersiz resim URL')).default([]),
  basePrice: z.number()
    .min(0, 'Fiyat 0 veya pozitif olmalı')
    .max(999999.99, 'Fiyat çok yüksek'),
  taxId: z.string().cuid('Geçersiz vergi ID'),
  costPrice: z.number()
    .min(0, 'Maliyet fiyatı 0 veya pozitif olmalı')
    .max(999999.99, 'Maliyet fiyatı çok yüksek')
    .optional(),
  profitMargin: z.number()
    .min(0, 'Kar marjı 0 veya pozitif olmalı')
    .max(100, 'Kar marjı maksimum %100 olabilir')
    .optional(),
  trackStock: z.boolean().default(false),
  unit: productUnitSchema.default('PIECE'),
  criticalStock: z.number()
    .min(0, 'Kritik stok 0 veya pozitif olmalı')
    .optional(),
  available: z.boolean().default(true),
  sellable: z.boolean().default(true),
  preparationTime: z.number()
    .int('Hazırlık süresi tam sayı olmalı')
    .min(0, 'Hazırlık süresi 0 veya pozitif olmalı')
    .max(1440, 'Hazırlık süresi maksimum 24 saat olabilir')
    .optional(),
  calories: z.number()
    .int('Kalori tam sayı olmalı')
    .min(0, 'Kalori 0 veya pozitif olmalı')
    .max(10000, 'Kalori çok yüksek')
    .optional(),
  allergens: z.array(z.string().max(50, 'Alerjen adı maksimum 50 karakter')).default([]),
  hasVariants: z.boolean().default(false),
  hasModifiers: z.boolean().default(false),
  showInMenu: z.boolean().default(true),
  featured: z.boolean().default(false),
  displayOrder: z.number()
    .int('Sıralama tam sayı olmalı')
    .min(0, 'Sıralama 0 veya pozitif olmalı')
    .default(0)
})

// Update Product Schema - Tüm alanlar optional
export const updateProductSchema = z.object({
  categoryId: z.string().min(1, 'Kategori ID gerekli').optional(),
  code: z.string()
    .min(1, 'Ürün kodu gerekli')
    .max(50, 'Ürün kodu maksimum 50 karakter olabilir')
    .regex(/^[A-Z0-9_-]+$/, 'Ürün kodu sadece büyük harf, rakam, tire ve alt çizgi içerebilir')
    .optional(),
  barcode: z.string()
    .max(50, 'Barkod maksimum 50 karakter olabilir')
    .optional(),
  name: z.string()
    .min(1, 'Ürün adı gerekli')
    .max(200, 'Ürün adı maksimum 200 karakter olabilir')
    .optional(),
  description: z.string()
    .max(1000, 'Açıklama maksimum 1000 karakter olabilir')
    .optional(),
  shortDescription: z.string()
    .max(200, 'Kısa açıklama maksimum 200 karakter olabilir')
    .optional(),
  image: z.string().url('Geçersiz resim URL').optional(),
  images: z.array(z.string().url('Geçersiz resim URL')).optional(),
  basePrice: z.number()
    .min(0, 'Fiyat 0 veya pozitif olmalı')
    .max(999999.99, 'Fiyat çok yüksek')
    .optional(),
  taxId: z.string().cuid('Geçersiz vergi ID').optional(),
  costPrice: z.number()
    .min(0, 'Maliyet fiyatı 0 veya pozitif olmalı')
    .max(999999.99, 'Maliyet fiyatı çok yüksek')
    .optional(),
  profitMargin: z.number()
    .min(0, 'Kar marjı 0 veya pozitif olmalı')
    .max(100, 'Kar marjı maksimum %100 olabilir')
    .optional(),
  trackStock: z.boolean().optional(),
  unit: productUnitSchema.optional(),
  criticalStock: z.number()
    .min(0, 'Kritik stok 0 veya pozitif olmalı')
    .optional(),
  available: z.boolean().optional(),
  sellable: z.boolean().optional(),
  preparationTime: z.number()
    .int('Hazırlık süresi tam sayı olmalı')
    .min(0, 'Hazırlık süresi 0 veya pozitif olmalı')
    .max(1440, 'Hazırlık süresi maksimum 24 saat olabilir')
    .optional(),
  calories: z.number()
    .int('Kalori tam sayı olmalı')
    .min(0, 'Kalori 0 veya pozitif olmalı')
    .max(10000, 'Kalori çok yüksek')
    .optional(),
  allergens: z.array(z.string().max(50, 'Alerjen adı maksimum 50 karakter')).optional(),
  hasVariants: z.boolean().optional(),
  hasModifiers: z.boolean().optional(),
  showInMenu: z.boolean().optional(),
  featured: z.boolean().optional(),
  displayOrder: z.number()
    .int('Sıralama tam sayı olmalı')
    .min(0, 'Sıralama 0 veya pozitif olmalı')
    .optional(),
  active: z.boolean().optional()
})

// Query Parameters Schema
export const productQuerySchema = z.object({
  page: z.string()
    .optional()
    .default('1')
    .transform(val => parseInt(val))
    .refine(val => val >= 1, 'Sayfa numarası 1 veya büyük olmalı'),
  limit: z.string()
    .optional()
    .default('20')
    .transform(val => parseInt(val))
    .refine(val => val >= 1 && val <= 100, 'Limit 1-100 arasında olmalı'),
  search: z.string()
    .max(100, 'Arama terimi maksimum 100 karakter')
    .optional(),
  categoryId: z.string().min(1, 'Kategori ID gerekli').optional(),
  available: z.string()
    .transform(val => val === 'true')
    .optional(),
  sellable: z.string()
    .transform(val => val === 'true')
    .optional(),
  featured: z.string()
    .transform(val => val === 'true')
    .optional(),
  hasVariants: z.string()
    .transform(val => val === 'true')
    .optional(),
  hasModifiers: z.string()
    .transform(val => val === 'true')
    .optional(),
  sortBy: z.enum(['name', 'basePrice', 'displayOrder', 'createdAt', 'updatedAt'])
    .default('displayOrder'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
})

// Product ID Parameter Schema
export const productIdSchema = z.object({
  id: z.string().cuid('Geçersiz ürün ID')
})

// ✅ Kural 13: Type inference
export type CreateProductInput = z.infer<typeof createProductSchema>
export type UpdateProductInput = z.infer<typeof updateProductSchema>
export type ProductQueryInput = z.infer<typeof productQuerySchema>
export type ProductIdInput = z.infer<typeof productIdSchema>

// ✅ Kural 13: Validation Middleware
export const validate = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Validation hatası',
          details: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        })
      }
      next(error)
    }
  }
}

// Query validation middleware
export const validateQuery = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Validate and store parsed values in a custom property
      const parsedQuery = schema.parse(req.query)
      ;(req as any).validatedQuery = parsedQuery
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Query parametreleri geçersiz',
          details: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        })
      }
      next(error)
    }
  }
}

// Params validation middleware
export const validateParams = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      req.params = schema.parse(req.params) as unknown as Request['params']
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'URL parametreleri geçersiz',
          details: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        })
      }
      next(error)
    }
  }
}
