// Category Service - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 7: API Service Pattern
// ✅ Kural 8: Error Handling
// ✅ Kural 12: Authentication Token Management

import { ApiResponse } from '@shared/types'

// ✅ Kural 8: AppError class kullanımı
class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message)
  }
}

// ✅ Category types
interface Category {
  id: string
  companyId: string
  parentId?: string | null
  name: string
  description?: string | null
  image?: string | null
  color?: string | null
  icon?: string | null
  showInKitchen: boolean
  preparationTime?: number | null
  displayOrder: number
  active: boolean
  showInMenu: boolean
  createdAt: string
  updatedAt: string
  parent?: {
    id: string
    name: string
  } | null
  children?: {
    id: string
    name: string
    displayOrder: number
  }[]
  _count?: {
    products: number
  }
}

interface CreateCategoryRequest {
  parentId?: string
  name: string
  description?: string
  image?: string
  color?: string
  icon?: string
  showInKitchen?: boolean
  preparationTime?: number
  displayOrder?: number
  showInMenu?: boolean
}

interface UpdateCategoryRequest {
  parentId?: string
  name?: string
  description?: string
  image?: string
  color?: string
  icon?: string
  showInKitchen?: boolean
  preparationTime?: number
  displayOrder?: number
  showInMenu?: boolean
  active?: boolean
}

interface CategoryQueryInput {
  page?: number
  limit?: number
  search?: string
  parentId?: string
  active?: boolean
  showInMenu?: boolean
  showInKitchen?: boolean
  sortBy?: 'name' | 'displayOrder' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
}

interface CategoryListResponse {
  success: boolean
  data: Category[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  message?: string
}

interface CategoryResponse {
  success: boolean
  data: Category
  message?: string
}

// ✅ API Base URL
const API_BASE_URL = '/api'

// ✅ Auth token helper
const getAuthToken = (): string | null => {
  return localStorage.getItem('auth_token')
}

// ✅ Request headers helper
const getHeaders = (): HeadersInit => {
  const token = getAuthToken()
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  }
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  
  return headers
}

// ✅ Error handler helper
const handleApiError = async (response: Response): Promise<never> => {
  let errorMessage = 'Bilinmeyen hata oluştu'
  let errorCode = 'UNKNOWN_ERROR'
  
  try {
    const errorData = await response.json()
    errorMessage = errorData.error || errorData.message || errorMessage
    errorCode = errorData.code || errorCode
  } catch {
    // JSON parse hatası, response text'ini kullan
    errorMessage = response.statusText || errorMessage
  }
  
  throw new AppError(errorMessage, response.status, errorCode)
}

// ✅ Kural 7: Category Service Implementation
class CategoryService {
  /**
   * ✅ Get categories with pagination and filters
   */
  async getCategories(query: CategoryQueryInput = {}): Promise<CategoryListResponse> {
    try {
      const searchParams = new URLSearchParams()
      
      // Add query parameters
      if (query.page) searchParams.append('page', query.page.toString())
      if (query.limit) searchParams.append('limit', query.limit.toString())
      if (query.search) searchParams.append('search', query.search)
      if (query.parentId) searchParams.append('parentId', query.parentId)
      if (query.active !== undefined) searchParams.append('active', query.active.toString())
      if (query.showInMenu !== undefined) searchParams.append('showInMenu', query.showInMenu.toString())
      if (query.showInKitchen !== undefined) searchParams.append('showInKitchen', query.showInKitchen.toString())
      if (query.sortBy) searchParams.append('sortBy', query.sortBy)
      if (query.sortOrder) searchParams.append('sortOrder', query.sortOrder)
      
      const url = `${API_BASE_URL}/categories?${searchParams.toString()}`
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getHeaders()
      })
      
      if (!response.ok) {
        await handleApiError(response)
      }
      
      return await response.json()
    } catch (error) {
      if (error instanceof AppError) {
        throw error
      }
      throw new AppError('Kategoriler alınırken hata oluştu', 500, 'FETCH_ERROR')
    }
  }

  /**
   * ✅ Get category by ID
   */
  async getCategoryById(id: string): Promise<CategoryResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
        method: 'GET',
        headers: getHeaders()
      })
      
      if (!response.ok) {
        await handleApiError(response)
      }
      
      return await response.json()
    } catch (error) {
      if (error instanceof AppError) {
        throw error
      }
      throw new AppError('Kategori alınırken hata oluştu', 500, 'FETCH_ERROR')
    }
  }

  /**
   * ✅ Create new category
   */
  async createCategory(data: CreateCategoryRequest): Promise<CategoryResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/categories`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify(data)
      })
      
      if (!response.ok) {
        await handleApiError(response)
      }
      
      return await response.json()
    } catch (error) {
      if (error instanceof AppError) {
        throw error
      }
      throw new AppError('Kategori oluşturulurken hata oluştu', 500, 'CREATE_ERROR')
    }
  }

  /**
   * ✅ Update category
   */
  async updateCategory(id: string, data: UpdateCategoryRequest): Promise<CategoryResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
        method: 'PUT',
        headers: getHeaders(),
        body: JSON.stringify(data)
      })
      
      if (!response.ok) {
        await handleApiError(response)
      }
      
      return await response.json()
    } catch (error) {
      if (error instanceof AppError) {
        throw error
      }
      throw new AppError('Kategori güncellenirken hata oluştu', 500, 'UPDATE_ERROR')
    }
  }

  /**
   * ✅ Delete category (soft delete)
   */
  async deleteCategory(id: string): Promise<ApiResponse<null>> {
    try {
      const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
        method: 'DELETE',
        headers: getHeaders()
      })
      
      if (!response.ok) {
        await handleApiError(response)
      }
      
      return await response.json()
    } catch (error) {
      if (error instanceof AppError) {
        throw error
      }
      throw new AppError('Kategori silinirken hata oluştu', 500, 'DELETE_ERROR')
    }
  }
}

// ✅ Export singleton instance
export const categoryService = new CategoryService()
export { AppError }
export type {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryQueryInput,
  CategoryListResponse,
  CategoryResponse
}
