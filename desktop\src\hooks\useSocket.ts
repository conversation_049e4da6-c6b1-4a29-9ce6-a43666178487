// Socket.io Hook - DEVELOPMENT_RULES.md Kural 11 & 17
// ✅ Performance optimized socket hook with event naming conventions

import { useEffect, useRef, useCallback, useMemo } from 'react'
import { io, Socket } from 'socket.io-client'
import { useAuthStore } from '../store/useAuthStore'
import { logger } from '../utils/logger'
// ✅ Import socket events using shared module
import {
  SOCKET_EVENTS,
  type SocketEvent,
  type OrderEventPayload,
  type KitchenEventPayload,
  type PaymentEventPayload,
  type TableEventPayload,
  type SystemEventPayload
} from '@shared/constants/socketEvents'

interface UseSocketOptions {
  autoConnect?: boolean
  reconnection?: boolean
  reconnectionAttempts?: number
  reconnectionDelay?: number
}

interface SocketEventHandlers {
  onOrderCreated?: (data: OrderEventPayload) => void
  onOrderUpdated?: (data: OrderEventPayload) => void
  onOrderDeleted?: (data: OrderEventPayload) => void
  onKitchenOrderNew?: (data: KitchenEventPayload) => void
  onKitchenOrderReady?: (data: KitchenEventPayload) => void
  onPaymentSuccess?: (data: PaymentEventPayload) => void
  onPaymentFailed?: (data: PaymentEventPayload) => void
  onTableStatusChanged?: (data: TableEventPayload) => void
  onSystemNotification?: (data: SystemEventPayload) => void
  onUserConnected?: (data: any) => void
  onUserDisconnected?: (data: any) => void
  onConnectionStatus?: (connected: boolean) => void
}

export const useSocket = (
  handlers: SocketEventHandlers = {},
  options: UseSocketOptions = {}
) => {
  const { user, token } = useAuthStore()
  const socketRef = useRef<Socket | null>(null)
  const handlersRef = useRef(handlers)

  // ✅ Kural 17: useMemo for socket configuration
  const socketConfig = useMemo(() => ({
    autoConnect: options.autoConnect ?? true,
    reconnection: options.reconnection ?? true,
    reconnectionAttempts: options.reconnectionAttempts ?? 5,
    reconnectionDelay: options.reconnectionDelay ?? 1000,
    transports: ['websocket', 'polling'],
    auth: {
      token: token
    }
  }), [options, token])

  // Update handlers ref when handlers change
  useEffect(() => {
    handlersRef.current = handlers
  }, [handlers])

  // ✅ Kural 17: useCallback for socket event emitters
  const emitEvent = useCallback((event: SocketEvent, data: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data)
      logger.debug('Socket event emitted', { event, data })
    } else {
      logger.warn('Socket not connected, event not emitted', { event })
    }
  }, [])

  const emitOrderCreate = useCallback((data: OrderEventPayload) => {
    emitEvent(SOCKET_EVENTS.ORDER_CREATE, data)
  }, [emitEvent])

  const emitOrderUpdate = useCallback((data: OrderEventPayload) => {
    emitEvent(SOCKET_EVENTS.ORDER_UPDATE, data)
  }, [emitEvent])

  const emitKitchenOrderReady = useCallback((data: KitchenEventPayload) => {
    emitEvent(SOCKET_EVENTS.KITCHEN_ORDER_READY, data)
  }, [emitEvent])

  const emitPaymentSuccess = useCallback((data: PaymentEventPayload) => {
    emitEvent(SOCKET_EVENTS.PAYMENT_SUCCESS, data)
  }, [emitEvent])

  const emitTableStatusChanged = useCallback((data: TableEventPayload) => {
    emitEvent(SOCKET_EVENTS.TABLE_STATUS_CHANGED, data)
  }, [emitEvent])

  // ✅ Socket connection management
  const connect = useCallback(() => {
    if (!user || !token) {
      logger.warn('Cannot connect socket: user or token missing')
      return
    }

    if (socketRef.current?.connected) {
      logger.info('Socket already connected')
      return
    }

    const serverUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000'
    
    logger.info('Connecting to socket server', { serverUrl, userId: user.id })

    socketRef.current = io(serverUrl, socketConfig)

    // ✅ Connection event handlers
    socketRef.current.on('connect', () => {
      logger.info('Socket connected', { socketId: socketRef.current?.id })
      handlersRef.current.onConnectionStatus?.(true)
    })

    socketRef.current.on('disconnect', (reason) => {
      logger.info('Socket disconnected', { reason })
      handlersRef.current.onConnectionStatus?.(false)
    })

    socketRef.current.on('connect_error', (error) => {
      logger.error('Socket connection error', { error: error.message })
      handlersRef.current.onConnectionStatus?.(false)
    })

    // ✅ Business event handlers
    socketRef.current.on(SOCKET_EVENTS.ORDER_CREATED, (data: OrderEventPayload) => {
      logger.debug('Order created event received', data)
      handlersRef.current.onOrderCreated?.(data)
    })

    socketRef.current.on(SOCKET_EVENTS.ORDER_UPDATED, (data: OrderEventPayload) => {
      logger.debug('Order updated event received', data)
      handlersRef.current.onOrderUpdated?.(data)
    })

    socketRef.current.on(SOCKET_EVENTS.ORDER_DELETED, (data: OrderEventPayload) => {
      logger.debug('Order deleted event received', data)
      handlersRef.current.onOrderDeleted?.(data)
    })

    socketRef.current.on(SOCKET_EVENTS.KITCHEN_ORDER_NEW, (data: KitchenEventPayload) => {
      logger.debug('Kitchen order new event received', data)
      handlersRef.current.onKitchenOrderNew?.(data)
    })

    socketRef.current.on(SOCKET_EVENTS.KITCHEN_ORDER_READY, (data: KitchenEventPayload) => {
      logger.debug('Kitchen order ready event received', data)
      handlersRef.current.onKitchenOrderReady?.(data)
    })

    socketRef.current.on(SOCKET_EVENTS.PAYMENT_SUCCESS, (data: PaymentEventPayload) => {
      logger.debug('Payment success event received', data)
      handlersRef.current.onPaymentSuccess?.(data)
    })

    socketRef.current.on(SOCKET_EVENTS.PAYMENT_FAILED, (data: PaymentEventPayload) => {
      logger.debug('Payment failed event received', data)
      handlersRef.current.onPaymentFailed?.(data)
    })

    socketRef.current.on(SOCKET_EVENTS.TABLE_STATUS_CHANGED, (data: TableEventPayload) => {
      logger.debug('Table status changed event received', data)
      handlersRef.current.onTableStatusChanged?.(data)
    })

    socketRef.current.on(SOCKET_EVENTS.SYSTEM_NOTIFICATION, (data: SystemEventPayload) => {
      logger.debug('System notification received', data)
      handlersRef.current.onSystemNotification?.(data)
    })

    socketRef.current.on(SOCKET_EVENTS.USER_CONNECTED, (data: any) => {
      logger.debug('User connected event received', data)
      handlersRef.current.onUserConnected?.(data)
    })

    socketRef.current.on(SOCKET_EVENTS.USER_DISCONNECTED, (data: any) => {
      logger.debug('User disconnected event received', data)
      handlersRef.current.onUserDisconnected?.(data)
    })

    // ✅ Heartbeat
    socketRef.current.on(SOCKET_EVENTS.HEARTBEAT, (data: any) => {
      logger.debug('Heartbeat received', data)
    })

    // Start heartbeat
    const heartbeatInterval = setInterval(() => {
      if (socketRef.current?.connected) {
        socketRef.current.emit(SOCKET_EVENTS.HEARTBEAT)
      }
    }, 30000) // 30 seconds

    // Store interval for cleanup
    ;(socketRef.current as any).heartbeatInterval = heartbeatInterval

  }, [user, token, socketConfig])

  const disconnect = useCallback(() => {
    if (socketRef.current) {
      // Clear heartbeat interval
      const interval = (socketRef.current as any).heartbeatInterval
      if (interval) {
        clearInterval(interval)
      }

      socketRef.current.disconnect()
      socketRef.current = null
      logger.info('Socket disconnected manually')
      handlersRef.current.onConnectionStatus?.(false)
    }
  }, [])

  // ✅ Auto connect/disconnect based on auth state
  useEffect(() => {
    if (user && token && socketConfig.autoConnect) {
      connect()
    } else {
      disconnect()
    }

    return () => {
      disconnect()
    }
  }, [user, token, connect, disconnect, socketConfig.autoConnect])

  // ✅ Kural 17: useMemo for return object
  return useMemo(() => ({
    socket: socketRef.current,
    connected: socketRef.current?.connected ?? false,
    connect,
    disconnect,
    emit: emitEvent,
    
    // Specific emitters
    emitOrderCreate,
    emitOrderUpdate,
    emitKitchenOrderReady,
    emitPaymentSuccess,
    emitTableStatusChanged
  }), [
    socketRef.current?.connected,
    connect,
    disconnect,
    emitEvent,
    emitOrderCreate,
    emitOrderUpdate,
    emitKitchenOrderReady,
    emitPaymentSuccess,
    emitTableStatusChanged
  ])
}

export default useSocket
