import { Request, Response, NextFunction } from 'express'
import { authService } from '../services/authService'
// Temporary local enum until shared types are properly configured
enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  BRANCH_MANAGER = 'BRANCH_MANAGER',
  CASHIER = 'CASHIER',
  WAITER = 'WAITER',
  KITCHEN = 'KITCHEN',
  REPORTER = 'REPORTER',
  COURIER = 'COURIER',
  CUSTOMER_SERVICE = 'CUSTOMER_SERVICE'
}

// Request interface'ini genişlet
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user?: {
        userId: string
        username: string
        role: string
        companyId: string
        branchId: string
      }
    }
  }
}

/**
 * JWT token doğrulama middleware'i
 */
export const authenticateToken = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Access token gerekli'
    })
  }

  const decoded = authService.verifyToken(token)
  if (!decoded) {
    return res.status(403).json({
      success: false,
      error: 'Geçersiz veya süresi dolmuş token'
    })
  }

  req.user = decoded
  next()
}

/**
 * Role-based authorization middleware'i
 */
export const authorizeRoles = (...allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Kimlik doğrulama gerekli'
      })
    }

    const userRole = req.user.role as UserRole
    
    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        error: 'Bu işlem için yetkiniz yok'
      })
    }

    next()
  }
}

/**
 * Admin yetkisi kontrolü
 */
export const requireAdmin = authorizeRoles(UserRole.SUPER_ADMIN, UserRole.ADMIN)

/**
 * Manager yetkisi kontrolü
 */
export const requireManager = authorizeRoles(
  UserRole.SUPER_ADMIN, 
  UserRole.ADMIN, 
  UserRole.BRANCH_MANAGER
)

/**
 * Cashier yetkisi kontrolü
 */
export const requireCashier = authorizeRoles(
  UserRole.SUPER_ADMIN,
  UserRole.ADMIN,
  UserRole.BRANCH_MANAGER,
  UserRole.CASHIER
)

/**
 * Şirket kontrolü - Kullanıcı sadece kendi şirketinin verilerine erişebilir
 */
export const checkCompanyAccess = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Kimlik doğrulama gerekli'
    })
  }

  // URL'den companyId parametresini al
  const requestedCompanyId = req.params.companyId || req.body.companyId || req.query.companyId

  if (requestedCompanyId && requestedCompanyId !== req.user.companyId) {
    // SUPER_ADMIN tüm şirketlere erişebilir
    if (req.user.role !== UserRole.SUPER_ADMIN) {
      return res.status(403).json({
        success: false,
        error: 'Bu şirketin verilerine erişim yetkiniz yok'
      })
    }
  }

  next()
}

/**
 * Şube kontrolü - Kullanıcı sadece kendi şubesinin verilerine erişebilir
 */
export const checkBranchAccess = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Kimlik doğrulama gerekli'
    })
  }

  const requestedBranchId = req.params.branchId || req.body.branchId || req.query.branchId

  if (requestedBranchId && requestedBranchId !== req.user.branchId) {
    // SUPER_ADMIN ve ADMIN tüm şubelere erişebilir
    const allowedRoles = [UserRole.SUPER_ADMIN, UserRole.ADMIN]
    if (!allowedRoles.includes(req.user.role as UserRole)) {
      return res.status(403).json({
        success: false,
        error: 'Bu şubenin verilerine erişim yetkiniz yok'
      })
    }
  }

  next()
}

/**
 * ✅ DEVELOPMENT_RULES.md Kural 8: Combined auth middleware
 * Authentication + Authorization in one function
 */
export const requireAuth = (allowedRoles?: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // 1. Token doğrulama
    const authHeader = req.headers['authorization']
    const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access token gerekli'
      })
    }

    const decoded = authService.verifyToken(token)
    if (!decoded) {
      return res.status(403).json({
        success: false,
        error: 'Geçersiz veya süresi dolmuş token'
      })
    }

    req.user = decoded

    // 2. Role kontrolü (eğer belirtilmişse)
    if (allowedRoles && allowedRoles.length > 0) {
      const userRole = req.user.role

      if (!allowedRoles.includes(userRole)) {
        return res.status(403).json({
          success: false,
          error: 'Bu işlem için yetkiniz yok'
        })
      }
    }

    next()
  }
}
