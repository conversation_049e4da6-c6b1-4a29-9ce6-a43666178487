// Variant Service - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 7: API Service Pattern
// ✅ Kural 8: Error Handling
// ✅ Kural 12: Authentication Token Management

import { ApiResponse, PaginatedResponse } from '@shared/types'

// ✅ Kural 8: AppError class kullanımı
class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message)
  }
}

// ✅ API Base URL
const API_BASE_URL = '/api'

// ✅ Auth token helper
const getAuthToken = (): string | null => {
  return localStorage.getItem('auth_token')
}

// ✅ Request headers helper
const getHeaders = (): HeadersInit => {
  const token = getAuthToken()
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  }
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  
  return headers
}

// ✅ Response handler with error handling
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    let errorMessage = 'Bilinmeyen hata'
    let errorCode = 'UNKNOWN_ERROR'
    
    try {
      const errorData = await response.json()
      errorMessage = errorData.error || errorData.message || errorMessage
      errorCode = errorData.code || errorCode
    } catch {
      // JSON parse hatası, HTTP status'a göre mesaj belirle
      switch (response.status) {
        case 401:
          errorMessage = 'Oturum süresi doldu'
          errorCode = 'UNAUTHORIZED'
          break
        case 403:
          errorMessage = 'Bu işlem için yetkiniz yok'
          errorCode = 'FORBIDDEN'
          break
        case 404:
          errorMessage = 'Kaynak bulunamadı'
          errorCode = 'NOT_FOUND'
          break
        case 500:
          errorMessage = 'Sunucu hatası'
          errorCode = 'SERVER_ERROR'
          break
        default:
          errorMessage = `HTTP ${response.status} hatası`
      }
    }
    
    throw new AppError(errorMessage, response.status, errorCode)
  }
  
  return await response.json()
}

// ✅ Query string builder
const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value))
    }
  })
  
  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}

// ✅ Variant Types
export interface ProductVariant {
  id: string
  productId: string
  name: string
  code: string
  sku?: string | null
  barcode?: string | null
  price: number
  costPrice?: number | null
  displayOrder: number
  active: boolean
  version: number
  createdAt: string
  updatedAt: string
  deletedAt?: string | null
  product?: {
    id: string
    name: string
    code: string
    companyId: string
  }
}

export interface VariantQueryInput {
  page?: number
  limit?: number
  search?: string
  active?: boolean
  sortBy?: 'name' | 'code' | 'price' | 'displayOrder' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
}

export interface CreateVariantRequest {
  name: string
  code: string
  sku?: string
  barcode?: string
  price: number
  costPrice?: number
  displayOrder?: number
  active?: boolean
}

export interface UpdateVariantRequest {
  name?: string
  code?: string
  sku?: string
  barcode?: string
  price?: number
  costPrice?: number
  displayOrder?: number
  active?: boolean
}

// ✅ Response Types
export type VariantResponse = ApiResponse<ProductVariant>
export type VariantListResponse = PaginatedResponse<ProductVariant>

// ✅ Variant Service Class
export class VariantService {
  
  /**
   * ✅ Kural 7: GET /api/products/:productId/variants - Paginated list
   */
  async getVariants(productId: string, query: VariantQueryInput = {}): Promise<VariantListResponse> {
    try {
      const queryString = buildQueryString(query)
      const url = `${API_BASE_URL}/products/${productId}/variants${queryString}`
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getHeaders(),
      })
      
      return await handleResponse<VariantListResponse>(response)
    } catch (error) {
      if (error instanceof AppError) throw error
      
      throw new AppError(
        'Varyantlar getirilemedi',
        500,
        'FETCH_VARIANTS_FAILED'
      )
    }
  }
  
  /**
   * ✅ Kural 7: GET /api/variants/:variantId - Single variant
   */
  async getVariantById(variantId: string): Promise<VariantResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/variants/${variantId}`, {
        method: 'GET',
        headers: getHeaders(),
      })
      
      return await handleResponse<VariantResponse>(response)
    } catch (error) {
      if (error instanceof AppError) throw error
      
      throw new AppError(
        'Varyant getirilemedi',
        500,
        'FETCH_VARIANT_FAILED'
      )
    }
  }
  
  /**
   * ✅ Kural 7: POST /api/products/:productId/variants - Create variant
   */
  async createVariant(productId: string, data: CreateVariantRequest): Promise<VariantResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/products/${productId}/variants`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify(data),
      })
      
      return await handleResponse<VariantResponse>(response)
    } catch (error) {
      if (error instanceof AppError) throw error
      
      throw new AppError(
        'Varyant oluşturulamadı',
        500,
        'CREATE_VARIANT_FAILED'
      )
    }
  }
  
  /**
   * ✅ Kural 7: PUT /api/variants/:variantId - Update variant
   */
  async updateVariant(variantId: string, data: UpdateVariantRequest): Promise<VariantResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/variants/${variantId}`, {
        method: 'PUT',
        headers: getHeaders(),
        body: JSON.stringify(data),
      })
      
      return await handleResponse<VariantResponse>(response)
    } catch (error) {
      if (error instanceof AppError) throw error
      
      throw new AppError(
        'Varyant güncellenemedi',
        500,
        'UPDATE_VARIANT_FAILED'
      )
    }
  }
  
  /**
   * ✅ Kural 7: DELETE /api/variants/:variantId - Delete variant
   */
  async deleteVariant(variantId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/variants/${variantId}`, {
        method: 'DELETE',
        headers: getHeaders(),
      })
      
      return await handleResponse<{ success: boolean; message: string }>(response)
    } catch (error) {
      if (error instanceof AppError) throw error
      
      throw new AppError(
        'Varyant silinemedi',
        500,
        'DELETE_VARIANT_FAILED'
      )
    }
  }
}

// ✅ Singleton instance
export const variantService = new VariantService()

// ✅ Export AppError for use in components
export { AppError }
