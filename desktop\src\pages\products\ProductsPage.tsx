// Products Page - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n kullanımı
// ✅ Kural 19: Tema sistemi kullanımı

import React, { useState, useEffect, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Typography,
  Button,
  Alert,
  Snackbar,
  Fab,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  Category as CategoryIcon
} from '@mui/icons-material'
import { useProductStore } from '../../store/useProductStore'
import { ProductList } from '../../components/products/ProductList'
import { ProductFilters } from '../../components/products/ProductFilters'
import { ProductForm } from '../../components/products/ProductForm'
import { CategoryFormModal } from '../../components/categories/CategoryFormModal'
import { Product, Category } from '@shared/types/ProductTypes'

// ✅ Kural 14: Component props interface
interface ProductsPageProps {
  // No props needed for this page component
}

// ✅ Kural 14: Standard component structure
export const ProductsPage: React.FC<ProductsPageProps> = () => {
  // ========== 1. HOOKS ==========
  // ✅ i18n hook
  const { t } = useTranslation()
  
  // ✅ Theme hook
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  // ✅ Store hooks
  const {
    categories,
    loading,
    error,
    fetchProducts,
    fetchCategories,
    fetchTaxes,
    clearError,
    refreshProducts,
    refreshCategories
  } = useProductStore()
  
  // ✅ Local state hooks
  const [formOpen, setFormOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [categoryFormOpen, setCategoryFormOpen] = useState(false)
  const [snackbarOpen, setSnackbarOpen] = useState(false)
  const [snackbarMessage, setSnackbarMessage] = useState('')
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'warning' | 'info'>('info')
  
  // ========== 2. COMPUTED VALUES ==========
  const isFormOpen = formOpen || editingProduct !== null
  const parentCategories = categories.filter(cat => !cat.parentId)
  
  // ========== 3. EVENT HANDLERS ==========
  
  /**
   * ✅ Handle add new product
   */
  const handleAddProduct = useCallback(() => {
    setEditingProduct(null)
    setFormOpen(true)
  }, [])

  /**
   * ✅ Handle add new category
   */
  const handleAddCategory = useCallback(() => {
    setCategoryFormOpen(true)
  }, [])
  
  /**
   * ✅ Handle edit product
   */
  const handleEditProduct = useCallback((product: Product) => {
    setEditingProduct(product)
    setFormOpen(true)
  }, [])
  
  /**
   * ✅ Handle close form
   */
  const handleCloseForm = useCallback(() => {
    setFormOpen(false)
    setEditingProduct(null)
  }, [])
  
  /**
   * ✅ Handle form success
   */
  const handleFormSuccess = useCallback((message: string) => {
    setSnackbarMessage(message)
    setSnackbarSeverity('success')
    setSnackbarOpen(true)
    handleCloseForm()
  }, [handleCloseForm])
  
  /**
   * ✅ Handle form error
   */
  const handleFormError = useCallback((message: string) => {
    setSnackbarMessage(message)
    setSnackbarSeverity('error')
    setSnackbarOpen(true)
  }, [])
  
  /**
   * ✅ Handle refresh
   */
  const handleRefresh = useCallback(async () => {
    try {
      await refreshProducts()
      setSnackbarMessage(t('products.messages.loadSuccess') || 'Ürünler yenilendi')
      setSnackbarSeverity('success')
      setSnackbarOpen(true)
    } catch (error) {
      setSnackbarMessage(t('products.messages.loadError') || 'Ürünler yenilenirken hata oluştu')
      setSnackbarSeverity('error')
      setSnackbarOpen(true)
    }
  }, [refreshProducts, t])
  
  /**
   * ✅ Handle category success
   */
  const handleCategorySuccess = useCallback((_category: Category, message: string) => {
    setCategoryFormOpen(false)
    setSnackbarMessage(message)
    setSnackbarSeverity('success')
    setSnackbarOpen(true)
    // Refresh categories to update dropdown
    refreshCategories()
  }, [refreshCategories])

  /**
   * ✅ Handle category success from product form
   */
  const handleCategorySuccessFromProduct = useCallback((message: string) => {
    setSnackbarMessage(message)
    setSnackbarSeverity('success')
    setSnackbarOpen(true)
    // Refresh categories to update dropdown
    refreshCategories()
    // Note: We don't close any modal here - product modal should stay open
  }, [refreshCategories])

  /**
   * ✅ Handle category error
   */
  const handleCategoryError = useCallback((message: string) => {
    setSnackbarMessage(message)
    setSnackbarSeverity('error')
    setSnackbarOpen(true)
  }, [])

  /**
   * ✅ Handle snackbar close
   */
  const handleSnackbarClose = useCallback(() => {
    setSnackbarOpen(false)
    clearError()
  }, [clearError])
  
  // ========== 4. EFFECTS ==========
  
  /**
   * ✅ Initialize data on mount
   */
  useEffect(() => {
    const initializeData = async () => {
      try {
        // Fetch initial data in parallel
        await Promise.all([
          fetchProducts(),
          fetchCategories(),
          fetchTaxes()
        ])
      } catch (error) {
        console.error('Failed to initialize products page:', error)
      }
    }
    
    initializeData()
  }, [fetchProducts, fetchCategories, fetchTaxes])
  
  /**
   * ✅ Handle store errors
   */
  useEffect(() => {
    if (error) {
      setSnackbarMessage(error)
      setSnackbarSeverity('error')
      setSnackbarOpen(true)
    }
  }, [error])
  
  // ========== 5. RENDER HELPERS ==========
  
  /**
   * ✅ Render page header
   */
  const renderHeader = () => (
    <Box sx={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      mb: 3,
      flexDirection: { xs: 'column', sm: 'row' },
      gap: { xs: 2, sm: 0 }
    }}>
      <Box>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('products.title')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('products.subtitle')}
        </Typography>
      </Box>
      
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
          disabled={loading}
          size={isMobile ? 'small' : 'medium'}
        >
          {t('common.refresh')}
        </Button>

        <Button
          variant="outlined"
          color="secondary"
          startIcon={<CategoryIcon />}
          onClick={handleAddCategory}
          disabled={loading}
          size={isMobile ? 'small' : 'medium'}
        >
          {t('products.actions.addCategory')}
        </Button>

        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAddProduct}
          disabled={loading}
          size={isMobile ? 'small' : 'medium'}
        >
          {t('products.actions.add')}
        </Button>
      </Box>
    </Box>
  )
  
  /**
   * ✅ Render floating action button (mobile)
   */
  const renderFab = () => {
    if (!isMobile) return null
    
    return (
      <Fab
        color="primary"
        aria-label={t('products.actions.add')}
        onClick={handleAddProduct}
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          zIndex: theme.zIndex.fab
        }}
      >
        <AddIcon />
      </Fab>
    )
  }
  
  // ========== 6. MAIN RENDER ==========
  return (
    <Box sx={{
      height: '100vh',
      width: '100vw',
      p: { xs: 1, sm: 2, md: 3 },
      backgroundColor: 'background.default',
      overflow: 'auto',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Page Header */}
      {renderHeader()}
      
      {/* Filters */}
      <Box sx={{ mb: 2 }}>
        <ProductFilters />
      </Box>
      
      {/* Product List */}
      <Box sx={{ flex: 1, minHeight: 0 }}>
        <ProductList 
          onEditProduct={handleEditProduct}
          onDeleteSuccess={(message) => {
            setSnackbarMessage(message)
            setSnackbarSeverity('success')
            setSnackbarOpen(true)
          }}
          onDeleteError={(message) => {
            setSnackbarMessage(message)
            setSnackbarSeverity('error')
            setSnackbarOpen(true)
          }}
        />
      </Box>
      
      {/* Product Form Dialog */}
      {isFormOpen && (
        <ProductForm
          open={isFormOpen}
          product={editingProduct}
          onClose={handleCloseForm}
          onSuccess={handleFormSuccess}
          onError={handleFormError}
          onCategorySuccess={handleCategorySuccessFromProduct}
        />
      )}

      {/* Category Form Dialog */}
      <CategoryFormModal
        open={categoryFormOpen}
        parentCategories={parentCategories}
        onClose={() => setCategoryFormOpen(false)}
        onSuccess={handleCategorySuccess}
        onError={handleCategoryError}
      />

      {/* Floating Action Button (Mobile) */}
      {renderFab()}
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  )
}
