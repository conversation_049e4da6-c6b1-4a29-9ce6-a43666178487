// Product Detail Page - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n Usage
// ✅ Kural 19: Material-UI Semantic Colors

import React, { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Typography,
  Button,
  Alert,
  Snackbar,
  Card,
  CardContent,
  Tabs,
  Tab,
  IconButton,
  Breadcrumbs,
  Link,
  useTheme,
  useMediaQuery,
  CircularProgress
} from '@mui/material'
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'
import { useProductStore } from '../../store/useProductStore'
import { useVariantStore } from '../../store/useVariantStore'
import { ProductForm } from '../../components/products/ProductForm'
import { VariantList } from '../../components/variants/VariantList'
import { VariantForm } from '../../components/variants/VariantForm'
import { VariantDeleteDialog } from '../../components/variants/VariantDeleteDialog'
import { Product } from '@shared/types/ProductTypes'
import { ProductVariant } from '../../types/variantTypes'

// ✅ Tab panel interface
interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

// ✅ Tab panel component
const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`product-tabpanel-${index}`}
      aria-labelledby={`product-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  )
}

// ✅ Kural 14: Standard component structure
export const ProductDetailPage: React.FC = () => {
  // ========== 1. HOOKS ==========
  // ✅ Router hooks
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  
  // ✅ i18n hook
  const { t } = useTranslation()
  
  // ✅ Theme hook
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  // ✅ Store hooks
  const {
    currentProduct,
    loading: productLoading,
    error: productError,
    fetchProductById,
    clearError: clearProductError
  } = useProductStore()
  
  const {
    variants,
    loading: variantLoading,
    error: variantError,
    currentVariant,
    isCreateModalOpen,
    isEditModalOpen,
    isDeleteDialogOpen,
    fetchVariants,
    setCurrentProductId,
    openEditModal,
    closeCreateModal,
    closeEditModal,
    clearError: clearVariantError,
    resetStore: resetVariantStore
  } = useVariantStore()
  
  // ========== 2. LOCAL STATE ==========
  const [tabValue, setTabValue] = useState(0)
  const [isProductFormOpen, setIsProductFormOpen] = useState(false)
  const [snackbarOpen, setSnackbarOpen] = useState(false)
  const [snackbarMessage, setSnackbarMessage] = useState('')
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success')
  
  // ========== 3. EVENT HANDLERS ==========
  
  /**
   * ✅ Handle tab change
   */
  const handleTabChange = useCallback((_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }, [])
  
  /**
   * ✅ Handle back navigation
   */
  const handleBack = useCallback(() => {
    navigate('/products')
  }, [navigate])
  
  /**
   * ✅ Handle product edit
   */
  const handleEditProduct = useCallback(() => {
    setIsProductFormOpen(true)
  }, [])
  
  /**
   * ✅ Handle variant edit
   */
  const handleEditVariant = useCallback((variant: ProductVariant) => {
    openEditModal(variant)
  }, [openEditModal])
  
  /**
   * ✅ Handle refresh
   */
  const handleRefresh = useCallback(async () => {
    if (!id) return
    
    try {
      await Promise.all([
        fetchProductById(id),
        fetchVariants(id)
      ])
    } catch (error) {
      console.error('Failed to refresh data:', error)
    }
  }, [id, fetchProductById, fetchVariants])
  
  /**
   * ✅ Handle snackbar close
   */
  const handleSnackbarClose = useCallback(() => {
    setSnackbarOpen(false)
  }, [])
  
  /**
   * ✅ Handle success messages
   */
  const handleSuccess = useCallback((message: string) => {
    setSnackbarMessage(message)
    setSnackbarSeverity('success')
    setSnackbarOpen(true)
  }, [])
  
  /**
   * ✅ Handle error messages
   */
  const handleError = useCallback((message: string) => {
    setSnackbarMessage(message)
    setSnackbarSeverity('error')
    setSnackbarOpen(true)
  }, [])
  
  // ========== 4. EFFECTS ==========
  
  /**
   * ✅ Initialize data on mount
   */
  useEffect(() => {
    if (!id) {
      navigate('/products')
      return
    }
    
    const initializeData = async () => {
      try {
        // Set current product ID for variant store
        setCurrentProductId(id)
        
        // Fetch product and variants in parallel
        await Promise.all([
          fetchProductById(id),
          fetchVariants(id)
        ])
      } catch (error) {
        console.error('Failed to initialize product detail page:', error)
      }
    }
    
    initializeData()
    
    // Cleanup on unmount
    return () => {
      resetVariantStore()
    }
  }, [id, navigate, fetchProductById, fetchVariants, setCurrentProductId, resetVariantStore])
  
  /**
   * ✅ Handle store errors
   */
  useEffect(() => {
    if (productError) {
      handleError(productError)
      clearProductError()
    }
  }, [productError, handleError, clearProductError])
  
  useEffect(() => {
    if (variantError) {
      handleError(variantError)
      clearVariantError()
    }
  }, [variantError, handleError, clearVariantError])
  
  // ========== 5. RENDER HELPERS ==========
  
  /**
   * ✅ Render breadcrumbs
   */
  const renderBreadcrumbs = () => (
    <Breadcrumbs sx={{ mb: 2 }}>
      <Link
        component="button"
        variant="body2"
        onClick={handleBack}
        sx={{ textDecoration: 'none' }}
      >
        {t('products.title')}
      </Link>
      <Typography variant="body2" color="text.primary">
        {currentProduct?.name || t('common.loading')}
      </Typography>
    </Breadcrumbs>
  )
  
  /**
   * ✅ Render header
   */
  const renderHeader = () => (
    <Box sx={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      mb: 3 
    }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <IconButton onClick={handleBack} color="primary">
          <ArrowBackIcon />
        </IconButton>
        
        <Box>
          <Typography variant="h4" component="h1">
            {currentProduct?.name || t('common.loading')}
          </Typography>
          {currentProduct && (
            <Typography variant="body2" color="text.secondary">
              {t('products.code')}: {currentProduct.code}
            </Typography>
          )}
        </Box>
      </Box>
      
      <Box sx={{ display: 'flex', gap: 1 }}>
        <IconButton
          onClick={handleRefresh}
          disabled={productLoading || variantLoading}
          color="primary"
        >
          <RefreshIcon />
        </IconButton>
        
        <Button
          variant="outlined"
          startIcon={<EditIcon />}
          onClick={handleEditProduct}
          disabled={!currentProduct}
        >
          {t('products.actions.edit')}
        </Button>
      </Box>
    </Box>
  )
  
  /**
   * ✅ Render product info
   */
  const renderProductInfo = () => {
    if (!currentProduct) return null
    
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('products.form.basicInfo')}
          </Typography>
          
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 2 }}>
            <Box>
              <Typography variant="caption" color="text.secondary">
                {t('products.name')}
              </Typography>
              <Typography variant="body1">
                {currentProduct.name}
              </Typography>
            </Box>
            
            <Box>
              <Typography variant="caption" color="text.secondary">
                {t('products.code')}
              </Typography>
              <Typography variant="body1" fontFamily="monospace">
                {currentProduct.code}
              </Typography>
            </Box>
            
            <Box>
              <Typography variant="caption" color="text.secondary">
                {t('products.category')}
              </Typography>
              <Typography variant="body1">
                {currentProduct.category?.name || '-'}
              </Typography>
            </Box>
            
            <Box>
              <Typography variant="caption" color="text.secondary">
                {t('products.basePrice')}
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                ₺{currentProduct.basePrice?.toFixed(2)}
              </Typography>
            </Box>
          </Box>
          
          {currentProduct.description && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="caption" color="text.secondary">
                {t('products.description')}
              </Typography>
              <Typography variant="body2">
                {currentProduct.description}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    )
  }
  
  // ========== 6. MAIN RENDER ==========
  
  // Loading state
  if (productLoading && !currentProduct) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        height: '50vh'
      }}>
        <CircularProgress />
      </Box>
    )
  }
  
  // Error state
  if (!currentProduct && !productLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {t('products.messages.loadError')}
        </Alert>
      </Box>
    )
  }
  
  return (
    <Box sx={{
      height: '100vh',
      width: '100vw',
      p: { xs: 1, sm: 2, md: 3 },
      backgroundColor: 'background.default',
      overflow: 'auto',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Breadcrumbs */}
      {renderBreadcrumbs()}
      
      {/* Header */}
      {renderHeader()}
      
      {/* Tabs */}
      <Card sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label={t('products.form.basicInfo')} />
            <Tab label={t('variants.title')} />
          </Tabs>
        </Box>
        
        {/* Tab Panels */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <TabPanel value={tabValue} index={0}>
            {renderProductInfo()}
          </TabPanel>
          
          <TabPanel value={tabValue} index={1}>
            {id && (
              <VariantList
                productId={id}
                onEditVariant={handleEditVariant}
                onDeleteSuccess={handleSuccess}
                onDeleteError={handleError}
              />
            )}
          </TabPanel>
        </Box>
      </Card>
      
      {/* Product Form Dialog */}
      {isProductFormOpen && (
        <ProductForm
          open={isProductFormOpen}
          product={currentProduct}
          onClose={() => setIsProductFormOpen(false)}
          onSuccess={(message) => {
            handleSuccess(message)
            if (id) fetchProductById(id)
          }}
          onError={handleError}
        />
      )}
      
      {/* Variant Form Dialog */}
      {(isCreateModalOpen || isEditModalOpen) && id && (
        <VariantForm
          open={isCreateModalOpen || isEditModalOpen}
          productId={id}
          variant={isEditModalOpen ? currentVariant : null}
          onClose={isCreateModalOpen ? closeCreateModal : closeEditModal}
          onSuccess={handleSuccess}
          onError={handleError}
        />
      )}
      
      {/* Variant Delete Dialog */}
      {isDeleteDialogOpen && (
        <VariantDeleteDialog
          open={isDeleteDialogOpen}
          variant={currentVariant}
          onClose={() => {}}
          onSuccess={handleSuccess}
          onError={handleError}
        />
      )}
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  )
}
