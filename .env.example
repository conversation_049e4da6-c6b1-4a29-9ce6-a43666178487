# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/restoran_pos"

# Server
PORT=3000
CLIENT_URL="http://localhost:5173"
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRE="7d"
SESSION_SECRET="your-session-secret-change-this"

# E-Archive
EARCHIVE_URL=""
EARCHIVE_USERNAME=""
EARCHIVE_PASSWORD=""

# SMS Provider
SMS_PROVIDER="netgsm"
SMS_API_KEY=""
SMS_API_SECRET=""
SMS_SENDER=""

# Email
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM=""

# File Storage
UPLOAD_PATH="./uploads"
MAX_FILE_SIZE="10485760"

# Redis (optional)
REDIS_URL="redis://localhost:6379"

# Development
NODE_ENV="development"
LOG_LEVEL="debug"
