// Variant Types and Validation - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 13: TypeScript Type Safety
// ✅ Kural 5: Zod Validation Schemas

import { z } from 'zod'

// ✅ Kural 13: Base Variant Interface
export interface ProductVariant {
  id: string
  productId: string
  name: string
  code: string
  sku?: string | null
  barcode?: string | null
  price: number
  costPrice?: number | null
  displayOrder: number
  active: boolean
  version: number
  createdAt: string
  updatedAt: string
  deletedAt?: string | null
  product?: {
    id: string
    name: string
    code: string
    companyId: string
  }
}

// ✅ Kural 5: Create Variant Validation Schema
export const createVariantSchema = z.object({
  name: z.string()
    .min(1, 'Varyant adı gerekli')
    .max(100, 'Varyant adı maksimum 100 karakter'),
  code: z.string()
    .min(1, 'Varyant kodu gerekli')
    .max(20, 'Varyant kodu maksimum 20 karakter')
    .regex(/^[A-Z0-9_-]+$/, 'Varyant kodu sadece büyük harf, rakam, tire ve alt çizgi içerebilir'),
  sku: z.string()
    .max(50, 'SKU maksimum 50 karakter')
    .regex(/^[A-Z0-9_-]*$/, 'SKU sadece büyük harf, rakam, tire ve alt çizgi içerebilir')
    .optional()
    .or(z.literal('')),
  barcode: z.string()
    .max(50, 'Barkod maksimum 50 karakter')
    .optional()
    .or(z.literal('')),
  price: z.number()
    .min(0, 'Fiyat 0 veya pozitif olmalı')
    .max(999999.99, 'Fiyat çok yüksek'),
  costPrice: z.number()
    .min(0, 'Maliyet fiyatı 0 veya pozitif olmalı')
    .max(999999.99, 'Maliyet fiyatı çok yüksek')
    .optional(),
  displayOrder: z.number()
    .int('Görüntüleme sırası tam sayı olmalı')
    .min(0, 'Görüntüleme sırası 0 veya pozitif olmalı')
    .default(0),
  active: z.boolean().default(true)
})

// ✅ Kural 5: Update Variant Validation Schema
export const updateVariantSchema = z.object({
  name: z.string()
    .min(1, 'Varyant adı gerekli')
    .max(100, 'Varyant adı maksimum 100 karakter')
    .optional(),
  code: z.string()
    .min(1, 'Varyant kodu gerekli')
    .max(20, 'Varyant kodu maksimum 20 karakter')
    .regex(/^[A-Z0-9_-]+$/, 'Varyant kodu sadece büyük harf, rakam, tire ve alt çizgi içerebilir')
    .optional(),
  sku: z.string()
    .max(50, 'SKU maksimum 50 karakter')
    .regex(/^[A-Z0-9_-]*$/, 'SKU sadece büyük harf, rakam, tire ve alt çizgi içerebilir')
    .optional()
    .or(z.literal('')),
  barcode: z.string()
    .max(50, 'Barkod maksimum 50 karakter')
    .optional()
    .or(z.literal('')),
  price: z.number()
    .min(0, 'Fiyat 0 veya pozitif olmalı')
    .max(999999.99, 'Fiyat çok yüksek')
    .optional(),
  costPrice: z.number()
    .min(0, 'Maliyet fiyatı 0 veya pozitif olmalı')
    .max(999999.99, 'Maliyet fiyatı çok yüksek')
    .optional(),
  displayOrder: z.number()
    .int('Görüntüleme sırası tam sayı olmalı')
    .min(0, 'Görüntüleme sırası 0 veya pozitif olmalı')
    .optional(),
  active: z.boolean().optional()
})

// ✅ Kural 5: Query Parameters Schema
export const variantQuerySchema = z.object({
  page: z.number()
    .int('Sayfa numarası tam sayı olmalı')
    .min(1, 'Sayfa numarası 1 veya büyük olmalı')
    .default(1),
  limit: z.number()
    .int('Limit tam sayı olmalı')
    .min(1, 'Limit 1 veya büyük olmalı')
    .max(100, 'Limit maksimum 100 olabilir')
    .default(20),
  search: z.string()
    .max(100, 'Arama terimi maksimum 100 karakter')
    .optional(),
  active: z.boolean().optional(),
  sortBy: z.enum(['name', 'code', 'price', 'displayOrder', 'createdAt', 'updatedAt'])
    .default('displayOrder'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
})

// ✅ Kural 13: Type inference from schemas
export type CreateVariantInput = z.infer<typeof createVariantSchema>
export type UpdateVariantInput = z.infer<typeof updateVariantSchema>
export type VariantQueryInput = z.infer<typeof variantQuerySchema>

// ✅ Form state types
export interface VariantFormData {
  name: string
  code: string
  sku: string
  barcode: string
  price: number | string
  costPrice: number | string
  displayOrder: number | string
  active: boolean
}

// ✅ UI State types
export interface VariantUIState {
  isLoading: boolean
  error: string | null
  selectedVariant: ProductVariant | null
  isCreateModalOpen: boolean
  isEditModalOpen: boolean
  isDeleteDialogOpen: boolean
}

// ✅ Table column definitions
export interface VariantTableColumn {
  id: keyof ProductVariant | 'actions'
  label: string
  minWidth?: number
  align?: 'left' | 'right' | 'center'
  format?: (value: any) => string
  sortable?: boolean
}

// ✅ Action types for state management
export type VariantAction = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_VARIANTS'; payload: ProductVariant[] }
  | { type: 'ADD_VARIANT'; payload: ProductVariant }
  | { type: 'UPDATE_VARIANT'; payload: ProductVariant }
  | { type: 'REMOVE_VARIANT'; payload: string }
  | { type: 'SET_SELECTED_VARIANT'; payload: ProductVariant | null }
  | { type: 'SET_CREATE_MODAL_OPEN'; payload: boolean }
  | { type: 'SET_EDIT_MODAL_OPEN'; payload: boolean }
  | { type: 'SET_DELETE_DIALOG_OPEN'; payload: boolean }

// ✅ Validation helper functions
export const validateVariantForm = (data: VariantFormData): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {}
  
  try {
    // Convert string numbers to numbers for validation
    const validationData = {
      ...data,
      price: typeof data.price === 'string' ? parseFloat(data.price) : data.price,
      costPrice: data.costPrice ? (typeof data.costPrice === 'string' ? parseFloat(data.costPrice as string) : data.costPrice) : undefined,
      displayOrder: typeof data.displayOrder === 'string' ? parseInt(data.displayOrder) : data.displayOrder,
      sku: data.sku || undefined,
      barcode: data.barcode || undefined
    }
    
    createVariantSchema.parse(validationData)
    return { isValid: true, errors: {} }
  } catch (error) {
    if (error instanceof z.ZodError) {
      error.issues.forEach((issue) => {
        const field = issue.path.join('.')
        errors[field] = issue.message
      })
    }
    return { isValid: false, errors }
  }
}

// ✅ Default form values
export const defaultVariantFormData: VariantFormData = {
  name: '',
  code: '',
  sku: '',
  barcode: '',
  price: '',
  costPrice: '',
  displayOrder: 0,
  active: true
}

// ✅ Currency formatter
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

// ✅ All schemas and types are already exported above
