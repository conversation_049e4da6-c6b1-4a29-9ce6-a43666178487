// Product Variant Validators - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 5: Zod Validation Schemas
// ✅ Kural 7: API Response Format
// ✅ Kural 13: TypeScript Type Safety

import { z } from 'zod'
import { Request, Response, NextFunction } from 'express'

// ✅ Kural 5: Create Variant Schema
export const createVariantSchema = z.object({
  name: z.string()
    .min(1, 'Varyant adı gerekli')
    .max(100, 'Varyant adı maksimum 100 karakter'),
  code: z.string()
    .min(1, 'Varyant kodu gerekli')
    .max(20, 'Varyant kodu maksimum 20 karakter')
    .regex(/^[A-Z0-9_-]+$/, 'Varyant kodu sadece bü<PERSON>ük harf, rakam, tire ve alt çizgi içerebilir'),
  sku: z.string()
    .max(50, 'SKU maksimum 50 karakter')
    .regex(/^[A-Z0-9_-]*$/, 'SKU sadece büyük harf, rakam, tire ve alt çizgi içerebilir')
    .optional(),
  barcode: z.string()
    .max(50, 'Barkod maksimum 50 karakter')
    .optional(),
  price: z.number()
    .min(0, 'Fiyat 0 veya pozitif olmalı')
    .max(999999.99, 'Fiyat çok yüksek'),
  costPrice: z.number()
    .min(0, 'Maliyet fiyatı 0 veya pozitif olmalı')
    .max(999999.99, 'Maliyet fiyatı çok yüksek')
    .optional(),
  displayOrder: z.number()
    .int('Görüntüleme sırası tam sayı olmalı')
    .min(0, 'Görüntüleme sırası 0 veya pozitif olmalı')
    .default(0),
  active: z.boolean().default(true)
})

// ✅ Kural 5: Update Variant Schema
export const updateVariantSchema = z.object({
  name: z.string()
    .min(1, 'Varyant adı gerekli')
    .max(100, 'Varyant adı maksimum 100 karakter')
    .optional(),
  code: z.string()
    .min(1, 'Varyant kodu gerekli')
    .max(20, 'Varyant kodu maksimum 20 karakter')
    .regex(/^[A-Z0-9_-]+$/, 'Varyant kodu sadece büyük harf, rakam, tire ve alt çizgi içerebilir')
    .optional(),
  sku: z.string()
    .max(50, 'SKU maksimum 50 karakter')
    .regex(/^[A-Z0-9_-]*$/, 'SKU sadece büyük harf, rakam, tire ve alt çizgi içerebilir')
    .optional(),
  barcode: z.string()
    .max(50, 'Barkod maksimum 50 karakter')
    .optional(),
  price: z.number()
    .min(0, 'Fiyat 0 veya pozitif olmalı')
    .max(999999.99, 'Fiyat çok yüksek')
    .optional(),
  costPrice: z.number()
    .min(0, 'Maliyet fiyatı 0 veya pozitif olmalı')
    .max(999999.99, 'Maliyet fiyatı çok yüksek')
    .optional(),
  displayOrder: z.number()
    .int('Görüntüleme sırası tam sayı olmalı')
    .min(0, 'Görüntüleme sırası 0 veya pozitif olmalı')
    .optional(),
  active: z.boolean().optional()
})

// ✅ Kural 5: Query Parameters Schema
export const variantQuerySchema = z.object({
  page: z.string()
    .optional()
    .default('1')
    .transform(val => parseInt(val))
    .refine(val => val >= 1, 'Sayfa numarası 1 veya büyük olmalı'),
  limit: z.string()
    .optional()
    .default('20')
    .transform(val => parseInt(val))
    .refine(val => val >= 1 && val <= 100, 'Limit 1-100 arasında olmalı'),
  search: z.string()
    .max(100, 'Arama terimi maksimum 100 karakter')
    .optional(),
  active: z.string()
    .transform(val => val === 'true')
    .optional(),
  sortBy: z.enum(['name', 'code', 'price', 'displayOrder', 'createdAt', 'updatedAt'])
    .default('displayOrder'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
})

// ✅ Kural 5: Product ID Parameter Schema
export const productIdSchema = z.object({
  productId: z.string().cuid('Geçersiz ürün ID')
})

// ✅ Kural 5: Variant ID Parameter Schema
export const variantIdSchema = z.object({
  variantId: z.string().cuid('Geçersiz varyant ID')
})

// ✅ Kural 13: Type inference
export type CreateVariantInput = z.infer<typeof createVariantSchema>
export type UpdateVariantInput = z.infer<typeof updateVariantSchema>
export type VariantQueryInput = z.infer<typeof variantQuerySchema>
export type ProductIdInput = z.infer<typeof productIdSchema>
export type VariantIdInput = z.infer<typeof variantIdSchema>

// ✅ Kural 5: Body Validation Middleware
export const validateBody = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Validation hatası',
          details: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        })
      }
      next(error)
    }
  }
}

// ✅ Kural 5: Query Validation Middleware - KRİTİK!
export const validateQuery = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Parse ve default değerlerle validate et
      const validatedQuery = schema.parse(req.query)
      ;(req as any).validatedQuery = validatedQuery
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Query validation failed',
          details: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        })
      }
    }
  }
}

// ✅ Kural 5: Params Validation Middleware
export const validateParams = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validatedParams = schema.parse(req.params)
      ;(req as any).validatedParams = validatedParams
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Parameter validation hatası',
          details: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        })
      }
      next(error)
    }
  }
}
