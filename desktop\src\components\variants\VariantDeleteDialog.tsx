// Variant Delete Dialog Component - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n Usage
// ✅ Kural 19: Material-UI Semantic Colors

import React, { useState, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Alert
} from '@mui/material'
import { useVariantStore } from '../../store/useVariantStore'
import { ProductVariant } from '../../types/variantTypes'

// ✅ Kural 14: Component props interface
interface VariantDeleteDialogProps {
  open: boolean
  variant: ProductVariant | null
  onClose: () => void
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

// ✅ Kural 14: Standard component structure
export const VariantDeleteDialog: React.FC<VariantDeleteDialogProps> = ({
  open,
  variant,
  onClose,
  onSuccess,
  onError
}) => {
  // ========== 1. HOOKS ==========
  // ✅ i18n hook
  const { t } = useTranslation()
  
  // ✅ Store hooks
  const { deleteVariant } = useVariantStore()
  
  // ✅ Local state hooks
  const [deleting, setDeleting] = useState(false)
  
  // ========== 2. EVENT HANDLERS ==========
  
  /**
   * ✅ Handle close
   */
  const handleClose = useCallback(() => {
    if (!deleting) {
      onClose()
    }
  }, [deleting, onClose])
  
  /**
   * ✅ Handle delete confirm
   */
  const handleDeleteConfirm = useCallback(async () => {
    if (!variant) return
    
    setDeleting(true)
    
    try {
      await deleteVariant(variant.id)
      onSuccess(t('variants.messages.deleteSuccess'))
      onClose()
    } catch (error: any) {
      const errorMessage = error?.message || t('variants.messages.deleteError')
      onError(errorMessage)
    } finally {
      setDeleting(false)
    }
  }, [variant, deleteVariant, onSuccess, onError, onClose, t])
  
  // ========== 3. MAIN RENDER ==========
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        {t('variants.form.delete')}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography>
            {t('variants.messages.deleteConfirm')}
          </Typography>
          
          {variant && (
            <Box sx={{ 
              p: 2, 
              backgroundColor: 'grey.50', 
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'grey.200'
            }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('variants.name')}: <strong>{variant.name}</strong>
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('variants.code')}: <strong>{variant.code}</strong>
              </Typography>
              {variant.sku && (
                <Typography variant="body2" color="text.secondary">
                  {t('variants.sku')}: <strong>{variant.sku}</strong>
                </Typography>
              )}
            </Box>
          )}
          
          <Alert severity="warning" sx={{ mt: 1 }}>
            <Typography variant="body2">
              {t('variants.messages.deleteWarning')}
            </Typography>
          </Alert>
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button 
          onClick={handleClose}
          disabled={deleting}
          color="inherit"
        >
          {t('variants.form.cancel')}
        </Button>
        
        <Button
          onClick={handleDeleteConfirm}
          color="error"
          variant="contained"
          disabled={deleting}
          startIcon={deleting ? <CircularProgress size={16} /> : undefined}
        >
          {deleting ? t('variants.form.deleting') : t('variants.form.delete')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}
