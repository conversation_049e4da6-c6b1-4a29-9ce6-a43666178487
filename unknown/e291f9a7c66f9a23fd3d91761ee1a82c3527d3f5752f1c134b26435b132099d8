{"name": "restoran-pos", "version": "1.0.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:desktop\"", "dev:server": "cd server && npm run dev", "dev:desktop": "cd desktop && npm run electron:dev", "build": "npm run build:server && npm run build:desktop", "build:server": "cd server && npm run build", "build:desktop": "cd desktop && npm run build", "dist": "cd desktop && npm run dist", "dist:win": "cd desktop && npm run dist:win", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "cd server && npm run seed", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "clean": "rm -rf node_modules desktop/node_modules server/node_modules shared/node_modules", "install:all": "npm install && cd desktop && npm install && cd ../server && npm install && cd ../shared && npm install", "lint": "cd server && npm run lint", "test": "cd server && npm test"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"concurrently": "^9.2.0", "prisma": "^6.11.1", "wait-on": "^8.0.3"}, "dependencies": {"@prisma/client": "^6.11.1"}, "prisma": {"seed": "tsx prisma/seed.ts"}}