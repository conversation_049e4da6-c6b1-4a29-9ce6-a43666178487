import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import tr from './locales/tr.json'
import en from './locales/en.json'

const resources = {
  tr: {
    translation: tr
  },
  en: {
    translation: en
  }
}

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'tr', // Varsayılan dil Türkçe
    fallbackLng: 'tr',
    
    interpolation: {
      escapeValue: false // React zaten XSS koruması sağlıyor
    },
    
    // Namespace kullanmıyoruz, tek translation objesi
    defaultNS: 'translation',
    
    // Debug mode (production'da false olacak)
    debug: process.env.NODE_ENV === 'development'
  })

export default i18n
