// 🎨 DESIGN SYSTEM CONSTANTS
// Bu dosyadan tüm tasarım sabitleri yönetiliyor

// 📏 SPACING (Boşluklar)
export const spacing = {
  xs: 4,    // 4px
  sm: 8,    // 8px
  md: 16,   // 16px
  lg: 24,   // 24px
  xl: 32,   // 32px
  xxl: 48,  // 48px
} as const

// 📐 BORDER RADIUS (Köşe yuvarlaklığı)
export const borderRadius = {
  none: 0,
  sm: 4,    // Küçük elementler
  md: 8,    // Butonlar, inputlar
  lg: 12,   // Kartlar
  xl: 16,   // Büyük kartlar
  full: 9999, // Tamamen yuvarlak
} as const

// 🔤 FONT SIZES (Yazı boyutları)
export const fontSize = {
  xs: '0.75rem',    // 12px
  sm: '0.875rem',   // 14px
  base: '1rem',     // 16px
  lg: '1.125rem',   // 18px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '1.875rem', // 30px
  '4xl': '2.25rem', // 36px
  '5xl': '3rem',    // 48px
} as const

// 📊 FONT WEIGHTS (Yazı kalınlığı)
export const fontWeight = {
  light: 300,
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
} as const

// 🌫️ SHADOWS (Gölgeler)
export const shadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
} as const

// 📱 BREAKPOINTS (Ekran boyutları)
export const breakpoints = {
  xs: 0,
  sm: 600,
  md: 900,
  lg: 1200,
  xl: 1536,
} as const

// 🎯 Z-INDEX (Katman sıralaması)
export const zIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
} as const

// 🏷️ POS SPECIFIC CONSTANTS (POS'a özel sabitler)
export const posConstants = {
  // Masa boyutları
  table: {
    minSize: 60,
    maxSize: 120,
    defaultSize: 80,
  },
  
  // Buton boyutları
  button: {
    small: { width: 80, height: 40 },
    medium: { width: 120, height: 48 },
    large: { width: 160, height: 56 },
  },
  
  // Sidebar genişlikleri
  sidebar: {
    collapsed: 60,
    expanded: 240,
  },
  
  // Header yüksekliği
  header: {
    height: 64,
  },
  
  // Footer yüksekliği
  footer: {
    height: 48,
  },
} as const

// 🎨 SEMANTIC COLORS (Anlamsal renkler)
export const semanticColors = {
  // Durum renkleri
  status: {
    success: '#10b981',    // Yeşil
    warning: '#f59e0b',    // Turuncu
    error: '#ef4444',      // Kırmızı
    info: '#3b82f6',       // Mavi
  },
  
  // POS durumları
  order: {
    pending: '#f59e0b',    // Bekleyen - Turuncu
    preparing: '#3b82f6',  // Hazırlanıyor - Mavi
    ready: '#10b981',      // Hazır - Yeşil
    delivered: '#6b7280',  // Teslim - Gri
    cancelled: '#ef4444',  // İptal - Kırmızı
  },
  
  // Masa durumları
  table: {
    empty: '#f3f4f6',      // Boş - Açık gri
    occupied: '#fef3c7',   // Dolu - Açık sarı
    reserved: '#dbeafe',   // Rezerve - Açık mavi
    cleaning: '#fde68a',   // Temizlik - Sarı
  },
  
  // Ödeme türleri
  payment: {
    cash: '#10b981',       // Nakit - Yeşil
    card: '#3b82f6',       // Kart - Mavi
    mobile: '#8b5cf6',     // Mobil - Mor
    voucher: '#f59e0b',    // Çek/Fiş - Turuncu
  },
} as const
