version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: pos_postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: restoran_pos
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: pos_redis
    ports:
      - "6379:6379"
    restart: unless-stopped

  adminer:
    image: adminer
    container_name: pos_adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
