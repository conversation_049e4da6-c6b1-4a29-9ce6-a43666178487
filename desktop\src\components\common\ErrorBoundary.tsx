// Error Boundary Component - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n Usage
// ✅ Kural 19: Material-UI Semantic Colors

import React, { Component, ErrorInfo, ReactNode } from 'react'
import {
  Box,
  Typography,
  <PERSON>ton,
  Alert,
  Card,
  CardContent,
  Stack
} from '@mui/material'
import {
  Refresh as RefreshIcon,
  BugReport as BugReportIcon
} from '@mui/icons-material'

// ✅ Error boundary props interface
interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

// ✅ Error boundary state interface
interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

// ✅ Error boundary class component
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    }
  }
  
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error
    }
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })
    
    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }
  
  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
  }
  
  handleReload = () => {
    window.location.reload()
  }
  
  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }
      
      // Default error UI
      return (
        <Box sx={{
          height: '100vh',
          width: '100vw',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'background.default',
          p: 3
        }}>
          <Card sx={{ maxWidth: 600, width: '100%' }}>
            <CardContent>
              <Stack spacing={3} alignItems="center" textAlign="center">
                <BugReportIcon 
                  sx={{ 
                    fontSize: 64, 
                    color: 'error.main' 
                  }} 
                />
                
                <Typography variant="h5" component="h1" color="error">
                  Bir Hata Oluştu
                </Typography>
                
                <Typography variant="body1" color="text.secondary">
                  Uygulama beklenmeyen bir hatayla karşılaştı. Lütfen sayfayı yenileyin veya tekrar deneyin.
                </Typography>
                
                {this.state.error && (
                  <Alert severity="error" sx={{ width: '100%', textAlign: 'left' }}>
                    <Typography variant="body2" component="pre" sx={{ 
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word',
                      fontFamily: 'monospace',
                      fontSize: '0.875rem'
                    }}>
                      {this.state.error.message}
                    </Typography>
                  </Alert>
                )}
                
                <Stack direction="row" spacing={2}>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={this.handleRetry}
                  >
                    Tekrar Dene
                  </Button>
                  
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={this.handleReload}
                  >
                    Sayfayı Yenile
                  </Button>
                </Stack>
                
                {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                  <Alert severity="info" sx={{ width: '100%', textAlign: 'left' }}>
                    <Typography variant="caption" component="pre" sx={{ 
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word',
                      fontFamily: 'monospace',
                      fontSize: '0.75rem'
                    }}>
                      {this.state.errorInfo.componentStack}
                    </Typography>
                  </Alert>
                )}
              </Stack>
            </CardContent>
          </Card>
        </Box>
      )
    }
    
    return this.props.children
  }
}

// ✅ Functional wrapper for easier usage
interface ErrorBoundaryWrapperProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

export const ErrorBoundaryWrapper: React.FC<ErrorBoundaryWrapperProps> = ({
  children,
  fallback,
  onError
}) => {
  return (
    <ErrorBoundary fallback={fallback} onError={onError}>
      {children}
    </ErrorBoundary>
  )
}

// ✅ Hook for error reporting
export const useErrorHandler = () => {
  const handleError = React.useCallback((error: Error, context?: string) => {
    console.error(`Error in ${context || 'component'}:`, error)
    
    // Here you could send error to logging service
    // Example: errorReportingService.report(error, context)
  }, [])
  
  return { handleError }
}
