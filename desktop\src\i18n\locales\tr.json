{"app": {"title": "Restoran POS Sistemi", "subtitle": "<PERSON><PERSON><PERSON> başarıyla tama<PERSON>landı!", "testButton": "Test <PERSON><PERSON>u"}, "menu": {"dashboard": "<PERSON>", "orders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "categories": "<PERSON><PERSON><PERSON>", "tables": "<PERSON><PERSON><PERSON>", "customers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inventory": "Stok", "kitchen": "Mutfak", "cashier": "<PERSON><PERSON>", "cariler": "<PERSON><PERSON>", "takeaway": "<PERSON><PERSON>", "quickSale": "Hızlı Satış"}, "dashboard": {"title": "<PERSON>", "welcome": "<PERSON><PERSON> Geldiniz", "notifications": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newOrder": "Yeni sipari<PERSON> var!", "orderReady": "Sipariş hazır", "tableRequest": "<PERSON><PERSON>bi", "kitchenAlert": "Mutfak uyarısı", "showAll": "<PERSON><PERSON><PERSON> bildirimleri göster", "noNotifications": "<PERSON>ni bildirim yok", "markAsRead": "<PERSON><PERSON><PERSON> olarak işaretle"}, "weather": {"title": "<PERSON><PERSON>", "temperature": "Sıcaklık", "condition": "Durum", "sunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cloudy": "Bulutlu", "rainy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snowy": "Karlı"}, "clock": {"currentTime": "<PERSON><PERSON> anki saat", "date": "<PERSON><PERSON><PERSON>"}}, "header": {"internet": {"label": "İnternet", "connected": "İnternet bağlantısı aktif", "disconnected": "İnternet bağlantısı yok"}, "backend": {"label": "<PERSON><PERSON><PERSON>", "connected": "Sunucu bağlantısı aktif", "disconnected": "<PERSON><PERSON><PERSON> bağlantısı yok"}, "user": {"menu": "Kullanıcı menüsü", "logout": "Çıkış Yap"}, "language": {"tooltip": "<PERSON><PERSON>", "turkish": "Türkçe", "english": "English"}, "theme": {"tooltip": "<PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>k tema", "dark": "<PERSON><PERSON> tema"}}, "common": {"save": "<PERSON><PERSON>", "cancel": "İptal", "delete": "Sil", "edit": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON>", "search": "Ara", "filter": "Filtrele", "export": "Dışa Aktar", "import": "İçe Aktar", "print": "Yazdır", "close": "Ka<PERSON><PERSON>", "open": "Aç", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "loading": "Yükleniyor...", "error": "<PERSON><PERSON>", "success": "Başarılı", "warning": "Uyarı", "info": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "none": "Hiç<PERSON>i"}, "auth": {"login": "<PERSON><PERSON><PERSON>", "logout": "Çıkış Yap", "username": "Kullanıcı Adı", "password": "Şifre", "rememberMe": "<PERSON><PERSON>", "forgotPassword": "Şif<PERSON><PERSON>", "welcome": "<PERSON><PERSON> Geldiniz", "welcomeBack": "<PERSON><PERSON><PERSON>", "loginToAccount": "Hesabınıza giriş yapın", "loginDescription": "Restoran POS sisteminize güvenli bir şekilde giriş ya<PERSON>ın", "enterUsername": "Kullanıcı adınızı girin", "enterPassword": "Şifrenizi girin", "loginButton": "<PERSON><PERSON><PERSON>", "loggingIn": "<PERSON><PERSON><PERSON> yapılıyor...", "loginSuccess": "Giriş başarılı! Yönlendiriliyorsunuz...", "loginError": "<PERSON><PERSON><PERSON> başar<PERSON>s<PERSON><PERSON>", "invalidCredentials": "Kullanıcı adı veya şifre hatalı", "accountLocked": "Hesabınız kilitlenmiş", "serverError": "<PERSON><PERSON><PERSON>, lütfen tekrar deneyin", "networkError": "Bağlantı hatası, internet bağlantınızı kontrol edin", "sessionExpired": "Oturumunuz sona erdi, lütfen tekrar giriş yapın", "hero": {"title": "Modern Restoran Yönetimi", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, masalarınızı ve satışlarınızı tek platformdan yönetin", "testimonial": "Bu sistem sayesinde restoran operasyonlarımız çok daha verimli hale geldi.", "author": "<PERSON><PERSON>", "position": "<PERSON><PERSON>"}}, "orders": {"newOrder": "<PERSON><PERSON>", "orderNumber": "Sipariş No", "table": "<PERSON><PERSON>", "customer": "Müş<PERSON>i", "total": "Toplam", "status": "Durum", "date": "<PERSON><PERSON><PERSON>", "pending": "Bekliyor", "preparing": "Hazırlanıyor", "ready": "Hazır", "delivered": "<PERSON><PERSON><PERSON>", "cancelled": "İptal Edildi"}, "products": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>i ve stok takibi", "name": "<PERSON><PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "basePrice": "<PERSON><PERSON>", "costPrice": "Maliyet Fiyatı", "profitMargin": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "stock": "Stok", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "shortDescription": "<PERSON><PERSON><PERSON>", "image": "Resim", "images": "<PERSON><PERSON><PERSON>", "barcode": "Barkod", "unit": "<PERSON><PERSON><PERSON>", "tax": "<PERSON><PERSON><PERSON>", "available": "Mevcut", "sellable": "Satılabilir", "trackStock": "Stok Takibi", "criticalStock": "K<PERSON>ik <PERSON>ok", "preparationTime": "Hazırlık Süresi", "calories": "<PERSON><PERSON><PERSON>", "allergens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasVariants": "Varyantları Var", "hasModifiers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showInMenu": "<PERSON><PERSON><PERSON>", "featured": "<PERSON><PERSON>", "displayOrder": "Görünt<PERSON><PERSON><PERSON>", "active": "Aktif", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list": {"title": "<PERSON><PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON><PERSON> ür<PERSON><PERSON> bulu<PERSON>", "emptySearch": "Arama kriterlerinize uygun ürün bulunamadı", "loading": "Ürünler yükleniyor...", "error": "Ürünler yüklenirken hata oluştu", "retry": "<PERSON><PERSON><PERSON>", "total": "Toplam {{count}} ü<PERSON><PERSON><PERSON>", "selected": "{{count}} <PERSON><PERSON><PERSON><PERSON>"}, "form": {"add": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "İptal", "save": "<PERSON><PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "deleting": "Siliniyor...", "basicInfo": "<PERSON><PERSON>", "pricing": "Fiya<PERSON><PERSON>rma", "categoryTax": "<PERSON><PERSON><PERSON> ve <PERSON>ergi", "additionalInfo": "<PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "validation": {"nameRequired": "<PERSON>rün adı gerekli", "nameMinLength": "Ürün adı en az 2 karakter olmalı", "nameMaxLength": "Ürün adı en fazla 200 karakter olabilir", "codeRequired": "<PERSON><PERSON><PERSON><PERSON> kodu gere<PERSON>li", "codeFormat": "<PERSON><PERSON><PERSON><PERSON> kodu sadece b<PERSON><PERSON><PERSON><PERSON> ha<PERSON>, r<PERSON><PERSON>, tire ve alt çizgi içerebilir", "codeMaxLength": "Ürün kodu en fazla 50 karakter olabilir", "barcodeMaxLength": "Barkod en fazla 50 karakter olabilir", "priceRequired": "<PERSON><PERSON><PERSON>", "priceMin": "Fiyat 0 veya pozitif olmalı", "priceMax": "Fiyat çok yüksek", "categoryRequired": "<PERSON><PERSON><PERSON> se<PERSON>imi gere<PERSON>", "taxRequired": "<PERSON><PERSON><PERSON> se<PERSON><PERSON> gere<PERSON>", "descriptionMaxLength": "Açıklama en fazla 1000 karakter olabilir", "shortDescriptionMaxLength": "Kısa açıklama en fazla 200 karakter olabilir", "imageUrl": "Geçersiz resim URL'si", "imageRequired": "<PERSON><PERSON><PERSON><PERSON> resmi gere<PERSON>", "imageSize": "<PERSON><PERSON><PERSON> boyutu <PERSON> büyük", "imageFormat": "Desteklenmeyen resim formatı", "costPriceMin": "Maliyet fiyatı 0 veya pozitif olmalı", "profitMarginRange": "Kar marjı 0-100 arasında olmalı", "criticalStockMin": "Kritik stok 0 veya pozitif olmalı", "preparationTimeMin": "Hazırlık süresi 0 veya pozitif olmalı", "caloriesMin": "Kalori 0 veya pozitif olmalı"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> adını girin", "code": "URUN_001", "barcode": "Barkod numarası", "description": "<PERSON><PERSON><PERSON><PERSON>", "shortDescription": "<PERSON><PERSON><PERSON>", "imageUrl": "https://example.com/image.jpg", "basePrice": "0.00", "costPrice": "0.00", "profitMargin": "0", "criticalStock": "10", "preparationTime": "5", "calories": "0", "allergens": "Gluten, Süt"}}, "filters": {"search": "<PERSON><PERSON><PERSON>n ara...", "searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> adı, kodu veya barkod ile ara", "category": "<PERSON><PERSON><PERSON>", "allCategories": "<PERSON><PERSON><PERSON>", "status": "Durum", "allStatuses": "<PERSON><PERSON><PERSON>", "available": "Mevcut", "unavailable": "<PERSON><PERSON><PERSON>", "sellable": "Satılabilir", "notSellable": "Satılamaz", "featured": "<PERSON><PERSON>", "hasVariants": "Varyantlı", "hasModifiers": "Modifierli", "trackStock": "Stok Takipli", "clearFilters": "<PERSON><PERSON><PERSON><PERSON>", "applyFilters": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"add": "<PERSON><PERSON><PERSON><PERSON>", "addCategory": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "duplicate": "Kopyala", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "export": "Dışa Aktar", "import": "İçe Aktar", "bulkEdit": "<PERSON>lu <PERSON>", "bulkDelete": "Toplu Sil"}, "messages": {"createSuccess": "<PERSON><PERSON>ün başarıyla oluşturuldu", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> başar<PERSON><PERSON> gü<PERSON>llendi", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "loadSuccess": "<PERSON>r<PERSON><PERSON><PERSON> başar<PERSON><PERSON> ye<PERSON>", "deleteConfirm": "<PERSON>u ürünü silmek istediğinizden emin misiniz?", "deleteWarning": "Bu işlem geri alınamaz", "bulkDeleteConfirm": "Se<PERSON><PERSON> {{count}} ürünü silmek istediğinizden emin misiniz?", "createError": "<PERSON><PERSON><PERSON><PERSON> olu<PERSON>ulurken hata oluştu", "updateError": "<PERSON><PERSON><PERSON><PERSON> gü<PERSON>en hata oluş<PERSON>", "deleteError": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>en hata o<PERSON>", "loadError": "Ürünler yüklenirken hata oluştu", "networkError": "Bağlantı hatası", "serverError": "<PERSON><PERSON><PERSON> hat<PERSON>ı", "validationError": "Form doğrulama hatası", "duplicateCode": "<PERSON>u ürün kodu zaten kullanılıyor", "duplicateBarcode": "<PERSON>u barkod zaten kullanılıyor"}, "units": {"PIECE": "<PERSON><PERSON>", "KG": "Kilogram", "GRAM": "Gram", "LITER": "Litre", "ML": "Mililitre", "PORTION": "Porsiyon", "BOX": "<PERSON><PERSON>", "PACKAGE": "<PERSON><PERSON>"}, "table": {"columns": {"image": "Resim", "name": "<PERSON><PERSON><PERSON><PERSON>", "code": "Kod", "category": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "stock": "Stok", "status": "Durum", "actions": "İşlemler"}, "noImage": "<PERSON><PERSON><PERSON>", "inStock": "Stok<PERSON>", "outOfStock": "Stok Yok", "lowStock": "Az Stok"}, "pagination": {"rowsPerPage": "<PERSON><PERSON> ba<PERSON><PERSON>na <PERSON>ı<PERSON>:", "of": "/", "page": "Say<PERSON>", "first": "İlk", "previous": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "last": "Son"}}, "variants": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Ürün <PERSON>larını yönetin", "name": "<PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON>", "sku": "SKU", "barcode": "Barkod", "price": "<PERSON><PERSON><PERSON>", "costPrice": "Maliyet Fiyatı", "displayOrder": "Görünt<PERSON><PERSON><PERSON>", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "status": "Durum", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list": {"title": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON><PERSON> bulu<PERSON>", "emptySearch": "Arama kriterlerinize uygun varyant bulunamadı", "loading": "Varyantlar yükleniyor...", "error": "Varyantlar yüklenirken hata oluş<PERSON>", "retry": "<PERSON><PERSON><PERSON>", "total": "Toplam {{count}} varyant", "selected": "{{count}} varyant seçili", "noVariants": "<PERSON>u <PERSON><PERSON><PERSON><PERSON> için henüz <PERSON> tanımlanmamış", "addFirstVariant": "İlk varyantı ekleyin"}, "form": {"add": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "cancel": "İptal", "save": "<PERSON><PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "deleting": "Siliniyor...", "basicInfo": "<PERSON><PERSON>", "pricing": "Fiya<PERSON><PERSON>rma", "settings": "<PERSON><PERSON><PERSON>"}, "fields": {"name": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Örn: Küçük Boy, Büyük Boy", "helper": "Varyantın gö<PERSON> adı", "required": "<PERSON><PERSON><PERSON> adı gerekli"}, "code": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Örn: S, M, L, XL", "helper": "Benzersiz varyant kodu", "required": "<PERSON><PERSON><PERSON> kodu gere<PERSON>li"}, "sku": {"label": "SKU", "placeholder": "Stok Takip Kodu", "helper": "Stok takip i<PERSON><PERSON> kod"}, "barcode": {"label": "Barkod", "placeholder": "Barkod numarası", "helper": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"label": "Satış Fiyatı", "placeholder": "0.00", "helper": "Varyantın satış fiyatı", "required": "<PERSON><PERSON><PERSON>"}, "costPrice": {"label": "Maliyet Fiyatı", "placeholder": "0.00", "helper": "Varyantın maliyet fiyatı"}, "displayOrder": {"label": "Görünt<PERSON><PERSON><PERSON>", "placeholder": "0", "helper": "Varyantların s<PERSON> dü<PERSON>"}, "active": {"label": "Akt<PERSON>", "helper": "Varyantın satışa sunulup sunulmayacağı"}}, "actions": {"add": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "activate": "Aktifleştir", "deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "table": {"columns": {"name": "Ad", "code": "Kod", "sku": "SKU", "barcode": "Barkod", "price": "<PERSON><PERSON><PERSON>", "costPrice": "Maliyet", "status": "Durum", "actions": "İşlemler"}}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> oluş<PERSON>uldu", "updateSuccess": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "deleteSuccess": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "loadSuccess": "Varyantlar başarı<PERSON> ye<PERSON>", "deleteConfirm": "Bu varyantı silmek istediğinizden emin misiniz?", "deleteWarning": "Bu işlem geri alınamaz", "createError": "<PERSON><PERSON><PERSON> oluşturulurken hata oluş<PERSON>", "updateError": "<PERSON><PERSON><PERSON> güncellenirken hata o<PERSON>", "deleteError": "<PERSON><PERSON><PERSON> si<PERSON>en hata <PERSON>", "loadError": "Varyantlar yüklenirken hata oluş<PERSON>", "validationError": "Form doğrulama hatası", "duplicateCode": "<PERSON>u varyant kodu zaten kullanılıyor", "duplicateSku": "Bu SKU zaten kullanılıyor", "duplicateBarcode": "<PERSON>u barkod zaten kullanılıyor"}}, "settings": {"general": "<PERSON><PERSON>", "language": "Dil", "theme": "<PERSON><PERSON>", "currency": "Para Birimi", "tax": "<PERSON><PERSON><PERSON>", "printer": "Yazıcı", "backup": "<PERSON><PERSON><PERSON><PERSON>"}, "categories": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "color": "Renk", "icon": "İkon", "parent": "Üst Kategori", "showInKitchen": "Mutfakta Göster", "showInMenu": "<PERSON><PERSON><PERSON>", "preparationTime": "Hazırlık Süresi", "displayOrder": "Sıralama", "active": "Aktif", "productCount": "<PERSON><PERSON><PERSON><PERSON>", "form": {"add": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "create": "Oluştur", "update": "<PERSON><PERSON><PERSON><PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "cancel": "İptal", "close": "Ka<PERSON><PERSON>", "basicInfo": "<PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "appearance": "G<PERSON>rü<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON> adını girin", "descriptionPlaceholder": "Kategori açıklamasını girin", "colorPlaceholder": "Ren<PERSON> seçin", "iconPlaceholder": "İkon seçin", "preparationTimePlaceholder": "Dakika cinsinden", "displayOrderPlaceholder": "Sıralama numarası", "validation": {"nameRequired": "Kategori adı gerekli", "nameMaxLength": "Kategori adı maksimum 100 karakter olabilir", "descriptionMaxLength": "Açıklama maksimum 500 karakter olabilir", "invalidColor": "Geçersiz renk kodu (örn: #FF0000)", "iconMaxLength": "İkon adı maksimum 50 karakter olabilir", "preparationTimeMin": "Hazırlık süresi 0 veya pozitif olmalı", "preparationTimeMax": "Hazırlık süresi maksimum 24 saat olabilir", "displayOrderMin": "Sıralama 0 veya pozitif olmalı"}}, "actions": {"add": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "<PERSON><PERSON>i başarıyla oluşturuldu", "updateSuccess": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteSuccess": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "createError": "Kategori oluşturulurken hata oluştu", "updateError": "<PERSON><PERSON><PERSON> hata o<PERSON>", "deleteError": "<PERSON><PERSON><PERSON> hata <PERSON>", "loadError": "Kategoriler yüklenirken hata o<PERSON>", "validationError": "Form verilerinde hata var"}, "confirmDelete": {"title": "<PERSON><PERSON><PERSON><PERSON>", "message": "Bu kategoriyi silmek istediğinizden emin misiniz?", "warning": "Bu işlem geri alınamaz.", "confirm": "Sil", "cancel": "İptal"}}}