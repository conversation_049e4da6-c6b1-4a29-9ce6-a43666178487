// Frontend Logger - DEVELOPMENT_RULES.md Kural 10
// ✅ Simple console logger for frontend with levels

type LogLevel = 'debug' | 'info' | 'warn' | 'error'

interface LogEntry {
  level: LogLevel
  message: string
  data?: any
  timestamp: string
  component?: string
}

class FrontendLogger {
  private isDevelopment = import.meta.env.DEV
  private logLevel: LogLevel = (import.meta.env.VITE_LOG_LEVEL as LogLevel) || 'info'

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    }

    return levels[level] >= levels[this.logLevel]
  }

  private formatMessage(entry: LogEntry): string {
    const prefix = `[${entry.timestamp}] [${entry.level.toUpperCase()}]`
    const component = entry.component ? ` [${entry.component}]` : ''
    return `${prefix}${component} ${entry.message}`
  }

  private log(level: LogLevel, message: string, data?: any, component?: string) {
    if (!this.shouldLog(level)) return

    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: new Date().toISOString(),
      component
    }

    const formattedMessage = this.formatMessage(entry)

    switch (level) {
      case 'debug':
        if (this.isDevelopment) {
          console.debug(formattedMessage, data || '')
        }
        break
      case 'info':
        console.info(formattedMessage, data || '')
        break
      case 'warn':
        console.warn(formattedMessage, data || '')
        break
      case 'error':
        console.error(formattedMessage, data || '')
        break
    }

    // In production, you might want to send errors to a logging service
    if (!this.isDevelopment && level === 'error') {
      // TODO: Send to external logging service
      // this.sendToLoggingService(entry)
    }
  }

  debug(message: string, data?: any, component?: string) {
    this.log('debug', message, data, component)
  }

  info(message: string, data?: any, component?: string) {
    this.log('info', message, data, component)
  }

  warn(message: string, data?: any, component?: string) {
    this.log('warn', message, data, component)
  }

  error(message: string, data?: any, component?: string) {
    this.log('error', message, data, component)
  }

  // ✅ Component-specific logger factory
  createComponentLogger(componentName: string) {
    return {
      debug: (message: string, data?: any) => this.debug(message, data, componentName),
      info: (message: string, data?: any) => this.info(message, data, componentName),
      warn: (message: string, data?: any) => this.warn(message, data, componentName),
      error: (message: string, data?: any) => this.error(message, data, componentName)
    }
  }
}

// ✅ Singleton instance
export const logger = new FrontendLogger()

// ✅ Default export
export default logger
