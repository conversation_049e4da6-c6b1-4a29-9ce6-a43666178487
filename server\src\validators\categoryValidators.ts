// Category Validators - DEVELOPMENT_RULES.md Kural 13'e uygun
// ✅ Zod Schema Pattern kullanımı

import { z } from 'zod'
import { Request, Response, NextFunction } from 'express'

// ✅ Kural 13: Zod Schema Pattern

// Create Category Schema
export const createCategorySchema = z.object({
  parentId: z.string().cuid('Geçersiz üst kategori ID').optional(),
  name: z.string()
    .min(1, 'Kategori adı gerekli')
    .max(100, 'Kategori adı maksimum 100 karakter olabilir'),
  description: z.string()
    .max(500, 'Açıklama maksimum 500 karakter olabilir')
    .optional(),
  image: z.string()
    .max(2000, 'Resim URL çok uzun')
    .optional(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Geçersiz renk kodu (örn: #FF0000)')
    .optional(),
  icon: z.string()
    .max(50, 'İkon adı maksimum 50 karakter olabilir')
    .optional(),
  showInKitchen: z.boolean().default(true),
  preparationTime: z.number()
    .int('Hazırlık süresi tam sayı olmalı')
    .min(0, 'Hazırlık süresi 0 veya pozitif olmalı')
    .max(1440, 'Hazırlık süresi maksimum 24 saat olabilir')
    .optional(),
  displayOrder: z.number()
    .int('Sıralama tam sayı olmalı')
    .min(0, 'Sıralama 0 veya pozitif olmalı')
    .default(0),
  showInMenu: z.boolean().default(true)
})

// Update Category Schema - Tüm alanlar optional
export const updateCategorySchema = z.object({
  parentId: z.string().cuid('Geçersiz üst kategori ID').optional(),
  name: z.string()
    .min(1, 'Kategori adı gerekli')
    .max(100, 'Kategori adı maksimum 100 karakter olabilir')
    .optional(),
  description: z.string()
    .max(500, 'Açıklama maksimum 500 karakter olabilir')
    .optional(),
  image: z.string()
    .max(2000, 'Resim URL çok uzun')
    .optional(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Geçersiz renk kodu (örn: #FF0000)')
    .optional(),
  icon: z.string()
    .max(50, 'İkon adı maksimum 50 karakter olabilir')
    .optional(),
  showInKitchen: z.boolean().optional(),
  preparationTime: z.number()
    .int('Hazırlık süresi tam sayı olmalı')
    .min(0, 'Hazırlık süresi 0 veya pozitif olmalı')
    .max(1440, 'Hazırlık süresi maksimum 24 saat olabilir')
    .optional(),
  displayOrder: z.number()
    .int('Sıralama tam sayı olmalı')
    .min(0, 'Sıralama 0 veya pozitif olmalı')
    .optional(),
  showInMenu: z.boolean().optional(),
  active: z.boolean().optional()
})

// Query Parameters Schema
export const categoryQuerySchema = z.object({
  page: z.string()
    .optional()
    .default('1')
    .transform(val => parseInt(val))
    .refine(val => val >= 1, 'Sayfa numarası 1 veya büyük olmalı'),
  limit: z.string()
    .optional()
    .default('50')
    .transform(val => parseInt(val))
    .refine(val => val >= 1 && val <= 100, 'Limit 1-100 arasında olmalı'),
  search: z.string()
    .max(100, 'Arama terimi maksimum 100 karakter')
    .optional(),
  parentId: z.string().cuid('Geçersiz üst kategori ID').optional(),
  active: z.string()
    .transform(val => val === 'true')
    .optional(),
  showInMenu: z.string()
    .transform(val => val === 'true')
    .optional(),
  showInKitchen: z.string()
    .transform(val => val === 'true')
    .optional(),
  sortBy: z.enum(['name', 'displayOrder', 'createdAt', 'updatedAt'])
    .default('displayOrder'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
})

// Category ID Parameter Schema
export const categoryIdSchema = z.object({
  id: z.string().cuid('Geçersiz kategori ID')
})

// ✅ Kural 13: Type inference
export type CreateCategoryInput = z.infer<typeof createCategorySchema>
export type UpdateCategoryInput = z.infer<typeof updateCategorySchema>
export type CategoryQueryInput = z.infer<typeof categoryQuerySchema>
export type CategoryIdInput = z.infer<typeof categoryIdSchema>

// ✅ Kural 13: Validation Middleware
export const validateBody = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Validation hatası',
          details: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        })
      }
      next(error)
    }
  }
}

// Query validation middleware
export const validateQuery = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // ✅ DEVELOPMENT_RULES.md Kural 5: validateQuery middleware pattern
      const validatedQuery = schema.parse(req.query)
      ;(req as any).validatedQuery = validatedQuery
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'Query parametreleri geçersiz',
          details: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        })
      }
      next(error)
    }
  }
}

// Params validation middleware
export const validateParams = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      req.params = schema.parse(req.params) as unknown as Request['params']
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: 'URL parametreleri geçersiz',
          details: error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message
          }))
        })
      }
      next(error)
    }
  }
}
