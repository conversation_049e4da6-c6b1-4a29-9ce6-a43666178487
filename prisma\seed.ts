import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // 1. Şirket oluştur
  const company = await prisma.company.upsert({
    where: { taxNumber: '1234567890' },
    update: {},
    create: {
      name: 'De<PERSON> Restoran',
      taxNumber: '1234567890',
      taxOffice: 'Kadıköy',
      address: 'Demo Adres, İstanbul',
      phone: '+90 212 123 45 67',
      email: '<EMAIL>',
      logo: null,
      website: 'https://demorestoran.com',
    },
  })

  // 2. Şube oluştur
  const branch = await prisma.branch.upsert({
    where: { 
      companyId_code: {
        companyId: company.id,
        code: 'MAIN01'
      }
    },
    update: {},
    create: {
      companyId: company.id,
      code: 'MAIN01',
      name: '<PERSON> Şube',
      address: 'Ana Şube Adresi, İstanbul',
      phone: '+90 212 123 45 67',
      email: '<EMAIL>',
      isMainBranch: true,
      openingTime: '08:00',
      closingTime: '23:00',
      workingDays: [1, 2, 3, 4, 5, 6, 7],
    },
  })

  // 3. Vergi oranları oluştur
  const tax8 = await prisma.tax.upsert({
    where: {
      companyId_code: {
        companyId: company.id,
        code: 'KDV8'
      }
    },
    update: {},
    create: {
      companyId: company.id,
      name: 'KDV %8',
      rate: 8.00,
      code: 'KDV8',
      type: 'VAT',
      isDefault: true,
      isIncluded: true,
    },
  })

  const tax18 = await prisma.tax.upsert({
    where: {
      companyId_code: {
        companyId: company.id,
        code: 'KDV18'
      }
    },
    update: {},
    create: {
      companyId: company.id,
      name: 'KDV %18',
      rate: 18.00,
      code: 'KDV18',
      type: 'VAT',
      isDefault: false,
      isIncluded: true,
    },
  })

  // 4. Kategoriler oluştur
  const beverageCategory = await prisma.category.upsert({
    where: { id: 'cat-beverages' },
    update: {},
    create: {
      id: 'cat-beverages',
      companyId: company.id,
      name: 'İçecekler',
      description: 'Sıcak ve soğuk içecekler',
      color: '#2196f3',
      icon: 'local_cafe',
      displayOrder: 1,
    },
  })

  const foodCategory = await prisma.category.upsert({
    where: { id: 'cat-food' },
    update: {},
    create: {
      id: 'cat-food',
      companyId: company.id,
      name: 'Yemekler',
      description: 'Ana yemekler ve atıştırmalıklar',
      color: '#ff9800',
      icon: 'restaurant',
      displayOrder: 2,
    },
  })

  // 5. Ürünler oluştur
  await prisma.product.upsert({
    where: {
      companyId_code: {
        companyId: company.id,
        code: 'COFFEE001'
      }
    },
    update: {},
    create: {
      companyId: company.id,
      categoryId: beverageCategory.id,
      code: 'COFFEE001',
      name: 'Türk Kahvesi',
      description: 'Geleneksel Türk kahvesi',
      basePrice: 25.00,
      taxId: tax8.id,
      unit: 'PIECE',
      preparationTime: 5,
      displayOrder: 1,
    },
  })

  await prisma.product.upsert({
    where: {
      companyId_code: {
        companyId: company.id,
        code: 'BURGER001'
      }
    },
    update: {},
    create: {
      companyId: company.id,
      categoryId: foodCategory.id,
      code: 'BURGER001',
      name: 'Klasik Burger',
      description: 'Et, marul, domates, soğan',
      basePrice: 85.00,
      taxId: tax18.id,
      unit: 'PIECE',
      preparationTime: 15,
      displayOrder: 1,
    },
  })

  // 6. Ödeme yöntemleri oluştur
  await prisma.paymentMethod.upsert({
    where: {
      companyId_code: {
        companyId: company.id,
        code: 'CASH'
      }
    },
    update: {},
    create: {
      companyId: company.id,
      name: 'Nakit',
      code: 'CASH',
      type: 'CASH',
      displayOrder: 1,
    },
  })

  await prisma.paymentMethod.upsert({
    where: {
      companyId_code: {
        companyId: company.id,
        code: 'CARD'
      }
    },
    update: {},
    create: {
      companyId: company.id,
      name: 'Kredi Kartı',
      code: 'CARD',
      type: 'CREDIT_CARD',
      displayOrder: 2,
    },
  })

  // 7. Masa alanları oluştur
  const mainArea = await prisma.tableArea.upsert({
    where: { id: 'area-main' },
    update: {},
    create: {
      id: 'area-main',
      branchId: branch.id,
      name: 'Ana Salon',
      description: 'Ana yemek salonu',
      displayOrder: 1,
    },
  })

  // 8. Masalar oluştur
  for (let i = 1; i <= 10; i++) {
    await prisma.table.upsert({
      where: {
        branchId_number: {
          branchId: branch.id,
          number: i.toString()
        }
      },
      update: {},
      create: {
        branchId: branch.id,
        areaId: mainArea.id,
        number: i.toString(),
        name: `Masa ${i}`,
        capacity: 4,
        positionX: (i % 5) * 100,
        positionY: Math.floor((i - 1) / 5) * 100,
      },
    })
  }

  // 9. Admin kullanıcı oluştur
  await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      companyId: company.id,
      branchId: branch.id,
      username: 'admin',
      password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      role: 'ADMIN',
    },
  })

  console.log('✅ Seeding completed!')
  console.log(`📊 Created:`)
  console.log(`   - Company: ${company.name}`)
  console.log(`   - Branch: ${branch.name}`)
  console.log(`   - Categories: 2`)
  console.log(`   - Products: 2`)
  console.log(`   - Tables: 10`)
  console.log(`   - Payment Methods: 2`)
  console.log(`   - Users: 1 (admin/password)`)
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
