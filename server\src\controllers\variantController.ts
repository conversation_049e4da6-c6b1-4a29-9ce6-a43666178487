// Product Variant Controller - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 6: Controller Layer <PERSON>
// ✅ Kural 7: Error Handling Pattern

import { Request, Response, NextFunction } from 'express'
import { variantService } from '../services/variantService'
import { AppError, ErrorCodes } from '../utils/AppError'
import { logger } from '../utils/logger'
import { 
  VariantQueryInput, 
  ProductIdInput, 
  VariantIdInput 
} from '../validators/variantValidators'

// ✅ Extend Request interface for user data
interface AuthenticatedRequest extends Request {
  user?: {
    userId: string
    username: string
    role: string
    companyId: string
    branchId: string
  }
}

/**
 * ✅ Kural 7: GET /api/products/:productId/variants - Variant listesi
 */
export const getVariants = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // Parameters and query are already validated by middleware
    const { productId } = (req as any).validatedParams as ProductIdInput
    const query = (req as any).validatedQuery as VariantQueryInput

    logger.info('Getting product variants', { 
      companyId, 
      productId,
      userId: req.user?.userId,
      query 
    })

    const result = await variantService.getVariants(companyId, productId, query)

    res.json(result)
  } catch (error) {
    logger.error('Get variants controller error', { 
      error, 
      companyId: req.user?.companyId,
      productId: req.params.productId,
      query: req.query 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: GET /api/variants/:variantId - Variant detayı
 */
export const getVariantById = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // Parameters are already validated by middleware
    const { variantId } = (req as any).validatedParams as VariantIdInput

    logger.info('Getting variant by ID', { 
      companyId, 
      variantId,
      userId: req.user?.userId
    })

    const result = await variantService.getVariantById(companyId, variantId)

    res.json(result)
  } catch (error) {
    logger.error('Get variant by ID controller error', { 
      error, 
      companyId: req.user?.companyId,
      variantId: req.params.variantId
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: POST /api/products/:productId/variants - Yeni variant oluştur
 */
export const createVariant = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // Parameters and request body are already validated by middleware
    const { productId } = (req as any).validatedParams as ProductIdInput
    const data = req.body

    logger.info('Creating variant', { 
      companyId, 
      productId,
      userId: req.user?.userId,
      variantCode: data.code 
    })

    const result = await variantService.createVariant(companyId, productId, data)

    res.status(201).json(result)
  } catch (error) {
    logger.error('Create variant controller error', { 
      error, 
      companyId: req.user?.companyId,
      productId: req.params.productId,
      data: req.body 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: PUT /api/variants/:variantId - Variant güncelle
 */
export const updateVariant = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // Parameters and request body are already validated by middleware
    const { variantId } = (req as any).validatedParams as VariantIdInput
    const data = req.body

    logger.info('Updating variant', { 
      companyId, 
      variantId,
      userId: req.user?.userId,
      data: { ...data, price: data.price ? 'updated' : 'unchanged' }
    })

    const result = await variantService.updateVariant(companyId, variantId, data)

    res.json(result)
  } catch (error) {
    logger.error('Update variant controller error', { 
      error, 
      companyId: req.user?.companyId,
      variantId: req.params.variantId,
      data: req.body 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: DELETE /api/variants/:variantId - Variant sil (soft delete)
 */
export const deleteVariant = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // Parameters are already validated by middleware
    const { variantId } = (req as any).validatedParams as VariantIdInput

    logger.info('Deleting variant', { 
      companyId, 
      variantId,
      userId: req.user?.userId
    })

    const result = await variantService.deleteVariant(companyId, variantId)

    res.json(result)
  } catch (error) {
    logger.error('Delete variant controller error', { 
      error, 
      companyId: req.user?.companyId,
      variantId: req.params.variantId
    })
    next(error)
  }
}
