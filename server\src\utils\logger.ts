// Logger - DEVELOPMENT_RULES.md Kural 15'e uygun
// ✅ <PERSON>

import winston from 'winston'

// ✅ Kural 15: <PERSON>
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'restoran-pos-server' },
  transports: [
    // Error log dosyası
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    // Combined log dosyası
    new winston.transports.File({ 
      filename: 'logs/combined.log',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
})

// Development ortamında console'a da log
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }))
}

// ✅ Kural 15: Logger kullanım örnekleri
// logger.info('User logged in', { userId: user.id })
// logger.error('Order creation failed', { error, orderData })
// logger.warn('Low stock warning', { productId, currentStock })
// logger.debug('Database query executed', { query, duration })
