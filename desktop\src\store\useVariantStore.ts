// Variant Store - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 15: Zustand Store Pattern
// ✅ Kural 8: Error Handling
// ✅ Kural 7: API Integration

import { create } from 'zustand'
import { 
  ProductVariant, 
  VariantQueryInput, 
  CreateVariantInput, 
  UpdateVariantInput 
} from '../types/variantTypes'
import { variantService, AppError } from '../services/variantService'
import { DEFAULT_PAGINATION } from '@shared/constants'

// ✅ Kural 15: Store interface
interface VariantStore {
  // State
  variants: ProductVariant[]
  currentVariant: ProductVariant | null
  currentProductId: string | null
  loading: boolean
  error: string | null
  
  // Pagination state
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  
  // Filter state
  filters: VariantQueryInput
  
  // UI State
  isCreateModalOpen: boolean
  isEditModalOpen: boolean
  isDeleteDialogOpen: boolean
  
  // Actions
  fetchVariants: (productId: string, query?: Partial<VariantQueryInput>) => Promise<void>
  fetchVariantById: (variantId: string) => Promise<void>
  createVariant: (productId: string, data: CreateVariantInput) => Promise<void>
  updateVariant: (variantId: string, data: UpdateVariantInput) => Promise<void>
  deleteVariant: (variantId: string) => Promise<void>
  
  // State management
  setCurrentVariant: (variant: ProductVariant | null) => void
  setCurrentProductId: (productId: string | null) => void
  setFilters: (filters: Partial<VariantQueryInput>) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  
  // Modal management
  openCreateModal: () => void
  closeCreateModal: () => void
  openEditModal: (variant: ProductVariant) => void
  closeEditModal: () => void
  openDeleteDialog: (variant: ProductVariant) => void
  closeDeleteDialog: () => void
  
  // Utility actions
  refreshVariants: () => Promise<void>
  resetStore: () => void
}

// ✅ Default query values
const defaultQuery: VariantQueryInput = {
  page: DEFAULT_PAGINATION.PAGE,
  limit: DEFAULT_PAGINATION.LIMIT,
  search: '',
  sortBy: 'displayOrder',
  sortOrder: 'asc'
}

// ✅ Kural 15: Zustand store implementation
export const useVariantStore = create<VariantStore>((set, get) => ({
  // ========== INITIAL STATE ==========
  variants: [],
  currentVariant: null,
  currentProductId: null,
  loading: false,
  error: null,
  
  pagination: {
    page: DEFAULT_PAGINATION.PAGE,
    limit: DEFAULT_PAGINATION.LIMIT,
    total: 0,
    totalPages: 0
  },
  
  filters: defaultQuery,
  
  // UI State
  isCreateModalOpen: false,
  isEditModalOpen: false,
  isDeleteDialogOpen: false,
  
  // ========== VARIANT ACTIONS ==========
  
  /**
   * ✅ Fetch variants with pagination and filters
   */
  fetchVariants: async (productId: string, query?: Partial<VariantQueryInput>) => {
    const state = get()
    const finalQuery = { ...state.filters, ...query }
    
    set({ 
      loading: true, 
      error: null, 
      currentProductId: productId 
    })
    
    try {
      const response = await variantService.getVariants(productId, finalQuery)
      
      set({
        variants: response.data || [],
        pagination: response.pagination || state.pagination,
        filters: finalQuery,
        loading: false,
        error: null
      })
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Varyantlar yüklenirken hata oluştu'
      
      set({
        variants: [],
        loading: false,
        error: errorMessage
      })
      
      throw error
    }
  },
  
  /**
   * ✅ Fetch single variant by ID
   */
  fetchVariantById: async (variantId: string) => {
    set({ loading: true, error: null })
    
    try {
      const response = await variantService.getVariantById(variantId)
      
      set({
        currentVariant: response.data || null,
        loading: false,
        error: null
      })
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Varyant yüklenirken hata oluştu'
      
      set({
        currentVariant: null,
        loading: false,
        error: errorMessage
      })
      
      throw error
    }
  },
  
  /**
   * ✅ Create new variant
   */
  createVariant: async (productId: string, data: CreateVariantInput) => {
    set({ loading: true, error: null })
    
    try {
      const response = await variantService.createVariant(productId, data)
      
      set(state => ({
        variants: [...state.variants, response.data],
        loading: false,
        error: null,
        isCreateModalOpen: false
      }))
      
      // Refresh variants to get updated pagination
      await get().refreshVariants()
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Varyant oluşturulurken hata oluştu'
      
      set({
        loading: false,
        error: errorMessage
      })
      
      throw error
    }
  },
  
  /**
   * ✅ Update variant
   */
  updateVariant: async (variantId: string, data: UpdateVariantInput) => {
    set({ loading: true, error: null })
    
    try {
      const response = await variantService.updateVariant(variantId, data)
      
      set(state => ({
        variants: state.variants.map(variant => 
          variant.id === variantId ? response.data : variant
        ),
        currentVariant: state.currentVariant?.id === variantId 
          ? response.data 
          : state.currentVariant,
        loading: false,
        error: null,
        isEditModalOpen: false
      }))
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Varyant güncellenirken hata oluştu'
      
      set({
        loading: false,
        error: errorMessage
      })
      
      throw error
    }
  },
  
  /**
   * ✅ Delete variant
   */
  deleteVariant: async (variantId: string) => {
    set({ loading: true, error: null })
    
    try {
      await variantService.deleteVariant(variantId)
      
      set(state => ({
        variants: state.variants.filter(variant => variant.id !== variantId),
        currentVariant: state.currentVariant?.id === variantId 
          ? null 
          : state.currentVariant,
        loading: false,
        error: null,
        isDeleteDialogOpen: false
      }))
      
      // Refresh variants to get updated pagination
      await get().refreshVariants()
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Varyant silinirken hata oluştu'
      
      set({
        loading: false,
        error: errorMessage
      })
      
      throw error
    }
  },
  
  // ========== STATE MANAGEMENT ==========
  
  /**
   * ✅ Set current variant
   */
  setCurrentVariant: (variant: ProductVariant | null) => {
    set({ currentVariant: variant })
  },
  
  /**
   * ✅ Set current product ID
   */
  setCurrentProductId: (productId: string | null) => {
    set({ currentProductId: productId })
  },
  
  /**
   * ✅ Set filters
   */
  setFilters: (filters: Partial<VariantQueryInput>) => {
    set(state => ({
      filters: { ...state.filters, ...filters }
    }))
  },
  
  /**
   * ✅ Clear error
   */
  clearError: () => {
    set({ error: null })
  },
  
  /**
   * ✅ Set loading state
   */
  setLoading: (loading: boolean) => {
    set({ loading })
  },
  
  // ========== MODAL MANAGEMENT ==========
  
  /**
   * ✅ Open create modal
   */
  openCreateModal: () => {
    set({ 
      isCreateModalOpen: true,
      currentVariant: null,
      error: null 
    })
  },
  
  /**
   * ✅ Close create modal
   */
  closeCreateModal: () => {
    set({ 
      isCreateModalOpen: false,
      currentVariant: null,
      error: null 
    })
  },
  
  /**
   * ✅ Open edit modal
   */
  openEditModal: (variant: ProductVariant) => {
    set({ 
      isEditModalOpen: true,
      currentVariant: variant,
      error: null 
    })
  },
  
  /**
   * ✅ Close edit modal
   */
  closeEditModal: () => {
    set({ 
      isEditModalOpen: false,
      currentVariant: null,
      error: null 
    })
  },
  
  /**
   * ✅ Open delete dialog
   */
  openDeleteDialog: (variant: ProductVariant) => {
    set({ 
      isDeleteDialogOpen: true,
      currentVariant: variant,
      error: null 
    })
  },
  
  /**
   * ✅ Close delete dialog
   */
  closeDeleteDialog: () => {
    set({ 
      isDeleteDialogOpen: false,
      currentVariant: null,
      error: null 
    })
  },
  
  /**
   * ✅ Refresh variants with current filters
   */
  refreshVariants: async () => {
    const state = get()
    if (state.currentProductId) {
      await state.fetchVariants(state.currentProductId, state.filters)
    }
  },
  
  /**
   * ✅ Reset store to initial state
   */
  resetStore: () => {
    set({
      variants: [],
      currentVariant: null,
      currentProductId: null,
      loading: false,
      error: null,
      pagination: {
        page: DEFAULT_PAGINATION.PAGE,
        limit: DEFAULT_PAGINATION.LIMIT,
        total: 0,
        totalPages: 0
      },
      filters: defaultQuery,
      isCreateModalOpen: false,
      isEditModalOpen: false,
      isDeleteDialogOpen: false
    })
  }
}))
