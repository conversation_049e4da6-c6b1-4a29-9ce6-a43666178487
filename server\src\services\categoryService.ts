// Category Service - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 9: Database Transactions Pattern
// ✅ Kural 7: Error Handling Pattern

import { PrismaClient } from '@prisma/client'
import { AppError, ErrorCodes } from '../utils/AppError'
import { logger } from '../utils/logger'
import { CreateCategoryInput, UpdateCategoryInput, CategoryQueryInput } from '../validators/categoryValidators'

const prisma = new PrismaClient()

// ✅ Kural 3: API Response Format
interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

interface PaginatedResponse<T> {
  success: boolean
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  message?: string
}

/**
 * ✅ Kural 6: Prisma Include Relations Pattern
 */
export const getCategories = async (companyId: string, query: CategoryQueryInput): Promise<PaginatedResponse<any>> => {
  try {
    const { page, limit, search, parentId, active, showInMenu, showInKitchen, sortBy, sortOrder } = query

    // Build where clause
    const where: any = {
      companyId,
      deletedAt: null
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (parentId !== undefined) {
      where.parentId = parentId
    }

    if (active !== undefined) {
      where.active = active
    }

    if (showInMenu !== undefined) {
      where.showInMenu = showInMenu
    }

    if (showInKitchen !== undefined) {
      where.showInKitchen = showInKitchen
    }

    // ✅ Kural 6: Include pattern
    const categories = await prisma.category.findMany({
      where,
      include: {
        parent: {
          select: {
            id: true,
            name: true
          }
        },
        children: {
          where: { deletedAt: null },
          select: {
            id: true,
            name: true,
            displayOrder: true
          },
          orderBy: { displayOrder: 'asc' }
        },
        _count: {
          select: {
            products: {
              where: { deletedAt: null }
            }
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.category.count({ where })

    logger.info('Categories retrieved successfully', {
      companyId,
      count: categories.length,
      total,
      query
    })

    return {
      success: true,
      data: categories,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    logger.error('Get categories service error', { error, companyId, query })
    throw new AppError(
      'Kategoriler alınırken hata oluştu',
      500,
      ErrorCodes.DATABASE_ERROR
    )
  }
}

/**
 * ✅ Kural 7: GET single category
 */
export const getCategoryById = async (companyId: string, categoryId: string): Promise<ApiResponse<any>> => {
  try {
    const category = await prisma.category.findFirst({
      where: {
        id: categoryId,
        companyId,
        deletedAt: null
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true
          }
        },
        children: {
          where: { deletedAt: null },
          select: {
            id: true,
            name: true,
            displayOrder: true
          },
          orderBy: { displayOrder: 'asc' }
        },
        _count: {
          select: {
            products: {
              where: { deletedAt: null }
            }
          }
        }
      }
    })

    if (!category) {
      throw new AppError(
        'Kategori bulunamadı',
        404,
        ErrorCodes.NOT_FOUND
      )
    }

    logger.info('Category retrieved successfully', {
      companyId,
      categoryId,
      categoryName: category.name
    })

    return {
      success: true,
      data: category,
      message: 'Kategori başarıyla alındı'
    }
  } catch (error) {
    if (error instanceof AppError) throw error
    
    logger.error('Get category by ID service error', { error, companyId, categoryId })
    throw new AppError(
      'Kategori alınırken hata oluştu',
      500,
      ErrorCodes.DATABASE_ERROR
    )
  }
}

/**
 * ✅ Kural 7: POST create category
 */
export const createCategory = async (companyId: string, data: CreateCategoryInput): Promise<ApiResponse<any>> => {
  try {
    // Check if parent category exists (if provided)
    if (data.parentId) {
      const parentCategory = await prisma.category.findFirst({
        where: {
          id: data.parentId,
          companyId,
          deletedAt: null
        }
      })

      if (!parentCategory) {
        throw new AppError(
          'Üst kategori bulunamadı',
          400,
          ErrorCodes.INVALID_INPUT
        )
      }
    }

    // Check for duplicate name in same company and parent
    const existingCategory = await prisma.category.findFirst({
      where: {
        companyId,
        parentId: data.parentId || null,
        name: data.name,
        deletedAt: null
      }
    })

    if (existingCategory) {
      throw new AppError(
        'Bu isimde bir kategori zaten mevcut',
        400,
        ErrorCodes.DUPLICATE_ENTRY
      )
    }

    const category = await prisma.category.create({
      data: {
        ...data,
        companyId
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    logger.info('Category created successfully', {
      companyId,
      categoryId: category.id,
      categoryName: category.name
    })

    return {
      success: true,
      data: category,
      message: 'Kategori başarıyla oluşturuldu'
    }
  } catch (error) {
    if (error instanceof AppError) throw error

    logger.error('Create category service error', { error, companyId, data })
    throw new AppError(
      'Kategori oluşturulurken hata oluştu',
      500,
      ErrorCodes.DATABASE_ERROR
    )
  }
}

/**
 * ✅ Kural 7: PUT update category
 */
export const updateCategory = async (companyId: string, categoryId: string, data: UpdateCategoryInput): Promise<ApiResponse<any>> => {
  try {
    // Check if category exists
    const existingCategory = await prisma.category.findFirst({
      where: {
        id: categoryId,
        companyId,
        deletedAt: null
      }
    })

    if (!existingCategory) {
      throw new AppError(
        'Kategori bulunamadı',
        404,
        ErrorCodes.NOT_FOUND
      )
    }

    // Check if parent category exists (if provided)
    if (data.parentId) {
      const parentCategory = await prisma.category.findFirst({
        where: {
          id: data.parentId,
          companyId,
          deletedAt: null
        }
      })

      if (!parentCategory) {
        throw new AppError(
          'Üst kategori bulunamadı',
          400,
          ErrorCodes.INVALID_INPUT
        )
      }

      // Prevent circular reference
      if (data.parentId === categoryId) {
        throw new AppError(
          'Kategori kendisinin alt kategorisi olamaz',
          400,
          ErrorCodes.INVALID_INPUT
        )
      }
    }

    // Check for duplicate name (if name is being updated)
    if (data.name && data.name !== existingCategory.name) {
      const duplicateCategory = await prisma.category.findFirst({
        where: {
          companyId,
          parentId: data.parentId !== undefined ? data.parentId : existingCategory.parentId,
          name: data.name,
          deletedAt: null,
          id: { not: categoryId }
        }
      })

      if (duplicateCategory) {
        throw new AppError(
          'Bu isimde bir kategori zaten mevcut',
          400,
          ErrorCodes.DUPLICATE_ENTRY
        )
      }
    }

    const category = await prisma.category.update({
      where: { id: categoryId },
      data: {
        ...data,
        updatedAt: new Date()
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    logger.info('Category updated successfully', {
      companyId,
      categoryId,
      categoryName: category.name
    })

    return {
      success: true,
      data: category,
      message: 'Kategori başarıyla güncellendi'
    }
  } catch (error) {
    if (error instanceof AppError) throw error

    logger.error('Update category service error', { error, companyId, categoryId, data })
    throw new AppError(
      'Kategori güncellenirken hata oluştu',
      500,
      ErrorCodes.DATABASE_ERROR
    )
  }
}

/**
 * ✅ Kural 7: DELETE category (soft delete)
 */
export const deleteCategory = async (companyId: string, categoryId: string): Promise<ApiResponse<any>> => {
  try {
    // Check if category exists
    const existingCategory = await prisma.category.findFirst({
      where: {
        id: categoryId,
        companyId,
        deletedAt: null
      },
      include: {
        _count: {
          select: {
            products: {
              where: { deletedAt: null }
            },
            children: {
              where: { deletedAt: null }
            }
          }
        }
      }
    })

    if (!existingCategory) {
      throw new AppError(
        'Kategori bulunamadı',
        404,
        ErrorCodes.NOT_FOUND
      )
    }

    // Check if category has products
    if (existingCategory._count.products > 0) {
      throw new AppError(
        'Bu kategoriye ait ürünler bulunduğu için silinemez',
        400,
        ErrorCodes.INVALID_OPERATION
      )
    }

    // Check if category has subcategories
    if (existingCategory._count.children > 0) {
      throw new AppError(
        'Bu kategorinin alt kategorileri bulunduğu için silinemez',
        400,
        ErrorCodes.INVALID_OPERATION
      )
    }

    // Soft delete
    await prisma.category.update({
      where: { id: categoryId },
      data: {
        deletedAt: new Date(),
        updatedAt: new Date()
      }
    })

    logger.info('Category deleted successfully', {
      companyId,
      categoryId,
      categoryName: existingCategory.name
    })

    return {
      success: true,
      message: 'Kategori başarıyla silindi'
    }
  } catch (error) {
    if (error instanceof AppError) throw error

    logger.error('Delete category service error', { error, companyId, categoryId })
    throw new AppError(
      'Kategori silinirken hata oluştu',
      500,
      ErrorCodes.DATABASE_ERROR
    )
  }
}
