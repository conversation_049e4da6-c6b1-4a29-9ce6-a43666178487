// Category Routes - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 8: Authentication middleware
// ✅ Kural 5: Validation middleware pattern

import { Router } from 'express'
import * as categoryController from '../controllers/categoryController'
import { requireAuth } from '../middlewares/authMiddleware'
import {
  createCategorySchema,
  updateCategorySchema,
  categoryQuerySchema,
  categoryIdSchema,
  validateBody,
  validateQuery,
  validateParams
} from '../validators/categoryValidators'

const router = Router()

// ✅ DEVELOPMENT_RULES.md Kural 8: All routes require authentication
// ✅ DEVELOPMENT_RULES.md Kural 5: Validation middleware pattern

/**
 * GET /api/categories
 * ✅ Query validation middleware kullanımı
 */
router.get(
  '/',
  requireAuth(['ADMIN', 'BRANCH_MANAGER', 'CASHIER', 'WAITER', 'KITCHEN']),
  validate<PERSON><PERSON><PERSON>(categoryQuerySchema),
  categoryController.getCategories
)

/**
 * GET /api/categories/:id
 * ✅ Params validation middleware kullanımı
 */
router.get(
  '/:id',
  requireAuth(['ADMIN', 'BRANCH_MANAGER', 'CASHIER', 'WAITER', 'KITCHEN']),
  validateParams(categoryIdSchema),
  categoryController.getCategoryById
)

/**
 * POST /api/categories
 * ✅ Body validation middleware kullanımı
 */
router.post(
  '/',
  requireAuth(['ADMIN', 'BRANCH_MANAGER']),
  validateBody(createCategorySchema),
  categoryController.createCategory
)

/**
 * PUT /api/categories/:id
 * ✅ Params + Body validation middleware kullanımı
 */
router.put(
  '/:id',
  requireAuth(['ADMIN', 'BRANCH_MANAGER']),
  validateParams(categoryIdSchema),
  validateBody(updateCategorySchema),
  categoryController.updateCategory
)

/**
 * DELETE /api/categories/:id
 * ✅ Params validation middleware kullanımı
 */
router.delete(
  '/:id',
  requireAuth(['ADMIN', 'BRANCH_MANAGER']),
  validateParams(categoryIdSchema),
  categoryController.deleteCategory
)

export default router
