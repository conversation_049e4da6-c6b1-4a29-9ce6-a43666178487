import { useState, useEffect } from 'react'

interface ConnectionStatus {
  internet: boolean
  backend: boolean
  lastChecked: Date | null
}

export const useConnectionStatus = () => {
  const [status, setStatus] = useState<ConnectionStatus>({
    internet: navigator.onLine,
    backend: false,
    lastChecked: null
  })

  // Internet connection monitoring
  useEffect(() => {
    const handleOnline = () => {
      setStatus(prev => ({ ...prev, internet: true }))
    }
    
    const handleOffline = () => {
      setStatus(prev => ({ ...prev, internet: false }))
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Backend connection monitoring
  useEffect(() => {
    const checkBackendConnection = async () => {
      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 5000)

        const response = await fetch('/health', {
          method: 'GET',
          signal: controller.signal
        })
        
        clearTimeout(timeoutId)
        
        setStatus(prev => ({ 
          ...prev, 
          backend: response.ok,
          lastChecked: new Date()
        }))
      } catch (error) {
        setStatus(prev => ({ 
          ...prev, 
          backend: false,
          lastChecked: new Date()
        }))
      }
    }

    // Initial check
    checkBackendConnection()

    // Check every 30 seconds
    const interval = setInterval(checkBackendConnection, 30000)

    return () => clearInterval(interval)
  }, [])

  return status
}
