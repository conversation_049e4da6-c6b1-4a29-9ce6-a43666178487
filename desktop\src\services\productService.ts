// Product Service - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 7: API Service Pattern
// ✅ Kural 8: Error Handling
// ✅ Kural 12: Authentication Token Management

import {
  Product,
  ProductQueryInput,
  CreateProductRequest,
  UpdateProductRequest,
  ProductListResponse,
  ProductResponse,
  Category,
  Tax,
  CategoryResponse,
  TaxResponse
} from '@shared/types/ProductTypes'
import { ApiResponse } from '@shared/types'
import { API_ENDPOINTS } from '@shared/constants'

// ✅ Kural 8: AppError class kullanımı
class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public code: string
  ) {
    super(message)
  }
}

// ✅ API Base URL
const API_BASE_URL = '/api'

// ✅ Auth token helper
const getAuthToken = (): string | null => {
  return localStorage.getItem('auth_token')
}

// ✅ Request headers helper
const getHeaders = (): HeadersInit => {
  const token = getAuthToken()
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  }
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  
  return headers
}

// ✅ Response handler with error handling
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    let errorMessage = 'Bilinmeyen hata'
    let errorCode = 'UNKNOWN_ERROR'
    
    try {
      const errorData = await response.json()
      errorMessage = errorData.error || errorData.message || errorMessage
      errorCode = errorData.code || errorCode
    } catch {
      // JSON parse hatası, HTTP status'a göre mesaj belirle
      switch (response.status) {
        case 401:
          errorMessage = 'Oturum süresi doldu'
          errorCode = 'UNAUTHORIZED'
          break
        case 403:
          errorMessage = 'Bu işlem için yetkiniz yok'
          errorCode = 'FORBIDDEN'
          break
        case 404:
          errorMessage = 'Kaynak bulunamadı'
          errorCode = 'NOT_FOUND'
          break
        case 500:
          errorMessage = 'Sunucu hatası'
          errorCode = 'SERVER_ERROR'
          break
        default:
          errorMessage = `HTTP ${response.status} hatası`
      }
    }
    
    throw new AppError(errorMessage, response.status, errorCode)
  }
  
  const data = await response.json()
  
  if (!data.success) {
    throw new AppError(
      data.error || 'İşlem başarısız',
      response.status,
      data.code || 'OPERATION_FAILED'
    )
  }
  
  return data
}

// ✅ Query string builder
const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value))
    }
  })
  
  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}

// ✅ Product Service Class
export class ProductService {
  
  /**
   * ✅ Kural 7: GET /api/products - Paginated list
   */
  async getProducts(query: ProductQueryInput): Promise<ProductListResponse> {
    try {
      const queryString = buildQueryString(query)
      const url = `${API_BASE_URL}/products${queryString}`
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getHeaders(),
      })
      
      return await handleResponse<ProductListResponse>(response)
    } catch (error) {
      if (error instanceof AppError) throw error
      
      throw new AppError(
        'Ürünler getirilemedi',
        500,
        'FETCH_PRODUCTS_FAILED'
      )
    }
  }
  
  /**
   * ✅ Kural 7: GET /api/products/:id - Single product
   */
  async getProductById(id: string): Promise<ProductResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/products/${id}`, {
        method: 'GET',
        headers: getHeaders(),
      })
      
      return await handleResponse<ProductResponse>(response)
    } catch (error) {
      if (error instanceof AppError) throw error
      
      throw new AppError(
        'Ürün getirilemedi',
        500,
        'FETCH_PRODUCT_FAILED'
      )
    }
  }
  
  /**
   * ✅ Kural 7: POST /api/products - Create product
   */
  async createProduct(data: CreateProductRequest): Promise<ProductResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/products`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify(data),
      })
      
      return await handleResponse<ProductResponse>(response)
    } catch (error) {
      if (error instanceof AppError) throw error
      
      throw new AppError(
        'Ürün oluşturulamadı',
        500,
        'CREATE_PRODUCT_FAILED'
      )
    }
  }
  
  /**
   * ✅ Kural 7: PUT /api/products/:id - Update product
   */
  async updateProduct(id: string, data: UpdateProductRequest): Promise<ProductResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/products/${id}`, {
        method: 'PUT',
        headers: getHeaders(),
        body: JSON.stringify(data),
      })
      
      return await handleResponse<ProductResponse>(response)
    } catch (error) {
      if (error instanceof AppError) throw error
      
      throw new AppError(
        'Ürün güncellenemedi',
        500,
        'UPDATE_PRODUCT_FAILED'
      )
    }
  }
  
  /**
   * ✅ Kural 7: DELETE /api/products/:id - Delete product
   */
  async deleteProduct(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/products/${id}`, {
        method: 'DELETE',
        headers: getHeaders(),
      })
      
      return await handleResponse<{ success: boolean; message: string }>(response)
    } catch (error) {
      if (error instanceof AppError) throw error
      
      throw new AppError(
        'Ürün silinemedi',
        500,
        'DELETE_PRODUCT_FAILED'
      )
    }
  }
  
  /**
   * ✅ GET /api/products/helpers/categories - Categories list
   */
  async getCategories(): Promise<ApiResponse<Category[]>> {
    try {
      const response = await fetch(`${API_BASE_URL}/products/helpers/categories`, {
        method: 'GET',
        headers: getHeaders(),
      })

      return await handleResponse<ApiResponse<Category[]>>(response)
    } catch (error) {
      if (error instanceof AppError) throw error

      throw new AppError(
        'Kategoriler getirilemedi',
        500,
        'FETCH_CATEGORIES_FAILED'
      )
    }
  }

  /**
   * ✅ GET /api/products/helpers/taxes - Taxes list
   */
  async getTaxes(): Promise<ApiResponse<Tax[]>> {
    try {
      const response = await fetch(`${API_BASE_URL}/products/helpers/taxes`, {
        method: 'GET',
        headers: getHeaders(),
      })

      return await handleResponse<ApiResponse<Tax[]>>(response)
    } catch (error) {
      if (error instanceof AppError) throw error

      throw new AppError(
        'Vergiler getirilemedi',
        500,
        'FETCH_TAXES_FAILED'
      )
    }
  }
}

// ✅ Singleton instance
export const productService = new ProductService()

// ✅ Export AppError for use in components
export { AppError }
