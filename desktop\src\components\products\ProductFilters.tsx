// Product Filters Component - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n kullanımı
// ✅ Kural 19: <PERSON><PERSON> siste<PERSON> kullanımı

import React, { useState, useCallback, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Card,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Grid,
  InputAdornment,
  useTheme,
  useMediaQuery,
  Collapse,
  IconButton,
  Typography
} from '@mui/material'
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material'
import { useProductStore } from '../../store/useProductStore'
import { ProductQueryInput } from '@shared/types/ProductTypes'

// ✅ Kural 14: Component props interface
interface ProductFiltersProps {
  // No props needed for this component
}

// ✅ Kural 14: Standard component structure
export const ProductFilters: React.FC<ProductFiltersProps> = () => {
  // ========== 1. HOOKS ==========
  // ✅ i18n hook
  const { t } = useTranslation()
  
  // ✅ Theme hook
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  // ✅ Store hooks
  const {
    categories,
    filters,
    loading,
    fetchProducts,
    setFilters
  } = useProductStore()
  
  // ✅ Local state hooks
  const [localFilters, setLocalFilters] = useState<ProductQueryInput>(filters)
  const [expanded, setExpanded] = useState(!isMobile)
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null)
  
  // ========== 2. COMPUTED VALUES ==========
  
  /**
   * ✅ Kural 17: useMemo for computed values
   */
  const hasActiveFilters = useMemo(() => {
    return !!(
      filters.search ||
      filters.categoryId ||
      filters.available !== undefined ||
      filters.sellable !== undefined ||
      filters.featured !== undefined
    )
  }, [filters])
  
  const activeFilterCount = useMemo(() => {
    let count = 0
    if (filters.search) count++
    if (filters.categoryId) count++
    if (filters.available !== undefined) count++
    if (filters.sellable !== undefined) count++
    if (filters.featured !== undefined) count++
    return count
  }, [filters])
  
  // ========== 3. EVENT HANDLERS ==========
  
  /**
   * ✅ Handle search input change with debounce
   */
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    setLocalFilters((prev: ProductQueryInput) => ({ ...prev, search: value }))
    
    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }
    
    // Set new timeout for debounced search
    const timeout = setTimeout(() => {
      setFilters({ search: value, page: 1 })
      fetchProducts({ search: value, page: 1 })
    }, 500)
    
    setSearchTimeout(timeout)
  }, [searchTimeout, setFilters, fetchProducts])
  
  /**
   * ✅ Handle category filter change
   */
  const handleCategoryChange = useCallback((event: any) => {
    const categoryId = event.target.value || undefined
    setLocalFilters((prev: ProductQueryInput) => ({ ...prev, categoryId }))
    setFilters({ categoryId, page: 1 })
    fetchProducts({ categoryId, page: 1 })
  }, [setFilters, fetchProducts])
  
  /**
   * ✅ Handle status filter change
   */
  const handleStatusChange = useCallback((field: keyof ProductQueryInput, value: boolean | undefined) => {
    setLocalFilters((prev: ProductQueryInput) => ({ ...prev, [field]: value }))
    setFilters({ [field]: value, page: 1 })
    fetchProducts({ [field]: value, page: 1 })
  }, [setFilters, fetchProducts])
  
  /**
   * ✅ Handle clear all filters
   */
  const handleClearFilters = useCallback(() => {
    const clearedFilters: Partial<ProductQueryInput> = {
      search: '',
      categoryId: undefined,
      available: undefined,
      sellable: undefined,
      featured: undefined,
      page: 1
    }
    
    setLocalFilters((prev: ProductQueryInput) => ({ ...prev, ...clearedFilters }))
    setFilters(clearedFilters)
    fetchProducts(clearedFilters)
  }, [setFilters, fetchProducts])
  
  /**
   * ✅ Handle expand/collapse
   */
  const handleToggleExpanded = useCallback(() => {
    setExpanded(prev => !prev)
  }, [])
  
  /**
   * ✅ Handle clear search
   */
  const handleClearSearch = useCallback(() => {
    setLocalFilters((prev: ProductQueryInput) => ({ ...prev, search: '' }))
    setFilters({ search: '', page: 1 })
    fetchProducts({ search: '', page: 1 })
  }, [setFilters, fetchProducts])
  
  // ========== 4. EFFECTS ==========
  
  /**
   * ✅ Sync local filters with store filters
   */
  useEffect(() => {
    setLocalFilters(filters)
  }, [filters])
  
  /**
   * ✅ Cleanup search timeout on unmount
   */
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
    }
  }, [searchTimeout])
  
  // ========== 5. RENDER HELPERS ==========
  
  /**
   * ✅ Render search field
   */
  const renderSearchField = () => (
    <TextField
      fullWidth
      placeholder={t('products.filters.searchPlaceholder')}
      value={localFilters.search || ''}
      onChange={handleSearchChange}
      disabled={loading}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <SearchIcon color="action" />
          </InputAdornment>
        ),
        endAdornment: localFilters.search && (
          <InputAdornment position="end">
            <IconButton
              size="small"
              onClick={handleClearSearch}
              edge="end"
            >
              <ClearIcon />
            </IconButton>
          </InputAdornment>
        )
      }}
    />
  )
  
  /**
   * ✅ Render category filter
   */
  const renderCategoryFilter = () => (
    <FormControl fullWidth>
      <InputLabel>{t('products.filters.category')}</InputLabel>
      <Select
        value={localFilters.categoryId || ''}
        onChange={handleCategoryChange}
        disabled={loading}
        label={t('products.filters.category')}
      >
        <MenuItem value="">
          {t('products.filters.allCategories')}
        </MenuItem>
        {categories.map((category) => (
          <MenuItem key={category.id} value={category.id}>
            {category.name}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
  
  /**
   * ✅ Render status filters
   */
  const renderStatusFilters = () => (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
      {/* Available Filter */}
      <Chip
        label={t('products.filters.available')}
        onClick={() => handleStatusChange('available', 
          localFilters.available === true ? undefined : true
        )}
        color={localFilters.available === true ? 'primary' : 'default'}
        variant={localFilters.available === true ? 'filled' : 'outlined'}
        disabled={loading}
      />
      
      {/* Sellable Filter */}
      <Chip
        label={t('products.filters.sellable')}
        onClick={() => handleStatusChange('sellable', 
          localFilters.sellable === true ? undefined : true
        )}
        color={localFilters.sellable === true ? 'primary' : 'default'}
        variant={localFilters.sellable === true ? 'filled' : 'outlined'}
        disabled={loading}
      />
      
      {/* Featured Filter */}
      <Chip
        label={t('products.filters.featured')}
        onClick={() => handleStatusChange('featured', 
          localFilters.featured === true ? undefined : true
        )}
        color={localFilters.featured === true ? 'primary' : 'default'}
        variant={localFilters.featured === true ? 'filled' : 'outlined'}
        disabled={loading}
      />
      
      {/* Has Variants Filter */}
      <Chip
        label={t('products.filters.hasVariants')}
        onClick={() => handleStatusChange('hasVariants', 
          localFilters.hasVariants === true ? undefined : true
        )}
        color={localFilters.hasVariants === true ? 'primary' : 'default'}
        variant={localFilters.hasVariants === true ? 'filled' : 'outlined'}
        disabled={loading}
      />
      
      {/* Track Stock Filter */}
      <Chip
        label={t('products.filters.trackStock')}
        onClick={() => handleStatusChange('trackStock', 
          localFilters.trackStock === true ? undefined : true
        )}
        color={localFilters.trackStock === true ? 'primary' : 'default'}
        variant={localFilters.trackStock === true ? 'filled' : 'outlined'}
        disabled={loading}
      />
    </Box>
  )
  
  /**
   * ✅ Render filter header
   */
  const renderFilterHeader = () => (
    <Box sx={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      mb: isMobile ? 1 : 0
    }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <FilterIcon color="action" />
        <Typography variant="subtitle2">
          {t('common.filter')}
        </Typography>
        {activeFilterCount > 0 && (
          <Chip
            label={activeFilterCount}
            size="small"
            color="primary"
          />
        )}
      </Box>
      
      <Box sx={{ display: 'flex', gap: 1 }}>
        {hasActiveFilters && (
          <Button
            size="small"
            onClick={handleClearFilters}
            disabled={loading}
          >
            {t('products.filters.clearFilters')}
          </Button>
        )}
        
        {isMobile && (
          <IconButton
            size="small"
            onClick={handleToggleExpanded}
          >
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        )}
      </Box>
    </Box>
  )
  
  // ========== 6. MAIN RENDER ==========
  return (
    <Card sx={{ p: 2 }}>
      {/* Filter Header */}
      {renderFilterHeader()}
      
      {/* Filter Content */}
      <Collapse in={expanded} timeout="auto">
        <Box sx={{ mt: isMobile ? 2 : 0 }}>
          <Grid container spacing={2} sx={{ alignItems: 'center' }}>
            {/* Search Field */}
            <Grid size={{ xs: 12, md: 4 }}>
              {renderSearchField()}
            </Grid>

            {/* Category Filter */}
            <Grid size={{ xs: 12, md: 3 }}>
              {renderCategoryFilter()}
            </Grid>

            {/* Status Filters */}
            <Grid size={{ xs: 12, md: 5 }}>
              {renderStatusFilters()}
            </Grid>
          </Grid>
        </Box>
      </Collapse>
    </Card>
  )
}
