// Socket.io Event Naming Conventions - DEVELOPMENT_RULES.md Kural 11
// ✅ Consistent event naming pattern

export const SOCKET_EVENTS = {
  // ========== ORDER EVENTS ==========
  // Client -> Server
  ORDER_CREATE: 'order:create',
  ORDER_UPDATE: 'order:update',
  ORDER_DELETE: 'order:delete',
  ORDER_CANCEL: 'order:cancel',
  ORDER_COMPLETE: 'order:complete',
  
  // Server -> Client
  ORDER_CREATED: 'order:created',
  ORDER_UPDATED: 'order:updated',
  ORDER_DELETED: 'order:deleted',
  ORDER_CANCELLED: 'order:cancelled',
  ORDER_COMPLETED: 'order:completed',
  
  // ========== KITCHEN EVENTS ==========
  // Real-time kitchen updates
  KITCHEN_ORDER_NEW: 'kitchen:order:new',
  KITCHEN_ORDER_READY: 'kitchen:order:ready',
  <PERSON><PERSON><PERSON>EN_ORDER_PREPARING: 'kitchen:order:preparing',
  K<PERSON><PERSON><PERSON>_ORDER_DELAYED: 'kitchen:order:delayed',
  
  // ========== PAYMENT EVENTS ==========
  PAYMENT_STARTED: 'payment:started',
  PAYMENT_SUCCESS: 'payment:success',
  PAYMENT_FAILED: 'payment:failed',
  PAYMENT_CANCELLED: 'payment:cancelled',
  PAYMENT_STATUS_CHANGED: 'payment:status:changed',
  
  // ========== TABLE EVENTS ==========
  TABLE_STATUS_CHANGED: 'table:status:changed',
  TABLE_OCCUPIED: 'table:occupied',
  TABLE_AVAILABLE: 'table:available',
  TABLE_RESERVED: 'table:reserved',
  
  // ========== INVENTORY EVENTS ==========
  INVENTORY_LOW_STOCK: 'inventory:low:stock',
  INVENTORY_OUT_OF_STOCK: 'inventory:out:stock',
  INVENTORY_UPDATED: 'inventory:updated',
  
  // ========== USER EVENTS ==========
  USER_CONNECTED: 'user:connected',
  USER_DISCONNECTED: 'user:disconnected',
  USER_STATUS_CHANGED: 'user:status:changed',
  
  // ========== SYSTEM EVENTS ==========
  SYSTEM_NOTIFICATION: 'system:notification',
  SYSTEM_ALERT: 'system:alert',
  SYSTEM_MAINTENANCE: 'system:maintenance',
  
  // ========== CONNECTION EVENTS ==========
  CONNECTION_STATUS: 'connection:status',
  HEARTBEAT: 'heartbeat',
  RECONNECT: 'reconnect'
} as const

// ✅ Type safety for events
export type SocketEvent = typeof SOCKET_EVENTS[keyof typeof SOCKET_EVENTS]

// ✅ Event payload types
export interface OrderEventPayload {
  orderId: string
  tableId?: string
  status?: string
  timestamp: string
  userId: string
}

export interface KitchenEventPayload {
  orderId: string
  items: Array<{
    productId: string
    quantity: number
    notes?: string
  }>
  estimatedTime?: number
  priority?: 'low' | 'normal' | 'high'
}

export interface PaymentEventPayload {
  orderId: string
  amount: number
  method: 'cash' | 'card' | 'digital'
  status: 'pending' | 'success' | 'failed' | 'cancelled'
  transactionId?: string
}

export interface TableEventPayload {
  tableId: string
  status: 'available' | 'occupied' | 'reserved' | 'cleaning'
  customerId?: string
  reservationTime?: string
}

export interface InventoryEventPayload {
  productId: string
  currentStock: number
  criticalStock: number
  unit: string
}

export interface SystemEventPayload {
  type: 'info' | 'warning' | 'error' | 'success'
  message: string
  timestamp: string
  userId?: string
  action?: string
}

// ✅ Room naming conventions
export const SOCKET_ROOMS = {
  // Company-wide rooms
  COMPANY: (companyId: string) => `company:${companyId}`,
  BRANCH: (branchId: string) => `branch:${branchId}`,
  
  // Role-based rooms
  KITCHEN: (branchId: string) => `kitchen:${branchId}`,
  CASHIER: (branchId: string) => `cashier:${branchId}`,
  MANAGER: (branchId: string) => `manager:${branchId}`,
  WAITER: (branchId: string) => `waiter:${branchId}`,
  
  // Feature-specific rooms
  ORDERS: (branchId: string) => `orders:${branchId}`,
  TABLES: (branchId: string) => `tables:${branchId}`,
  INVENTORY: (branchId: string) => `inventory:${branchId}`,
  
  // User-specific room
  USER: (userId: string) => `user:${userId}`
} as const

// ✅ Socket middleware types
export interface SocketUser {
  id: string
  username: string
  role: string
  companyId: string
  branchId: string
}

export interface AuthenticatedSocket {
  user: SocketUser
  join: (room: string) => void
  leave: (room: string) => void
  emit: (event: SocketEvent, data: any) => void
  broadcast: {
    to: (room: string) => {
      emit: (event: SocketEvent, data: any) => void
    }
  }
}
