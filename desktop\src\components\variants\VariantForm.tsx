// Variant Form Component - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n Usage
// ✅ Kural 19: Material-UI Semantic Colors

import React, { useState, useCallback, useMemo, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Grid,
  Divider,
  FormControlLabel,
  Switch,
  CircularProgress,
  InputAdornment,
  useTheme,
  useMediaQuery
} from '@mui/material'
import { useVariantStore } from '../../store/useVariantStore'
import { 
  ProductVariant, 
  VariantFormData, 
  defaultVariantFormData,
  validateVariantForm 
} from '../../types/variantTypes'

// ✅ Kural 14: Component props interface
interface VariantFormProps {
  open: boolean
  productId: string
  variant?: ProductVariant | null
  onClose: () => void
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

// ✅ Validation errors interface
interface ValidationErrors {
  [key: string]: string
}

// ✅ Kural 14: Standard component structure
export const VariantForm: React.FC<VariantFormProps> = ({
  open,
  productId,
  variant,
  onClose,
  onSuccess,
  onError
}) => {
  // ========== 1. HOOKS ==========
  // ✅ i18n hook
  const { t } = useTranslation()
  
  // ✅ Theme hook
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  // ✅ Store hooks
  const {
    loading,
    createVariant,
    updateVariant
  } = useVariantStore()
  
  // ✅ Local state hooks
  const [formData, setFormData] = useState<VariantFormData>(defaultVariantFormData)
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [submitting, setSubmitting] = useState(false)
  
  // ========== 2. COMPUTED VALUES ==========
  
  /**
   * ✅ Kural 17: useMemo for computed values
   */
  const isEditing = useMemo(() => !!variant, [variant])
  
  const dialogTitle = useMemo(() => {
    return isEditing ? t('variants.form.edit') : t('variants.form.add')
  }, [isEditing, t])
  
  const submitButtonText = useMemo(() => {
    if (submitting) {
      return t('variants.form.saving')
    }
    return isEditing ? t('variants.form.update') : t('variants.form.create')
  }, [submitting, isEditing, t])
  
  // ========== 3. EVENT HANDLERS ==========
  
  /**
   * ✅ Handle input change
   */
  const handleInputChange = useCallback((field: keyof VariantFormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = event.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }, [errors])
  
  /**
   * ✅ Handle switch change
   */
  const handleSwitchChange = useCallback((field: keyof VariantFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.checked
    setFormData(prev => ({ ...prev, [field]: value }))
  }, [])
  
  /**
   * ✅ Handle close
   */
  const handleClose = useCallback(() => {
    if (!submitting) {
      onClose()
    }
  }, [submitting, onClose])
  
  /**
   * ✅ Handle submit
   */
  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault()
    
    // Validate form
    const validation = validateVariantForm(formData)
    if (!validation.isValid) {
      setErrors(validation.errors)
      return
    }
    
    setSubmitting(true)
    setErrors({})
    
    try {
      // Prepare API data
      const apiData = {
        name: formData.name.trim(),
        code: formData.code.trim().toUpperCase(),
        sku: formData.sku.trim() || undefined,
        barcode: formData.barcode.trim() || undefined,
        price: typeof formData.price === 'string' ? parseFloat(formData.price) : formData.price,
        costPrice: formData.costPrice ? 
          (typeof formData.costPrice === 'string' ? parseFloat(formData.costPrice as string) : formData.costPrice) : 
          undefined,
        displayOrder: typeof formData.displayOrder === 'string' ? 
          parseInt(formData.displayOrder) : 
          formData.displayOrder,
        active: formData.active
      }
      
      if (isEditing && variant) {
        await updateVariant(variant.id, apiData)
        onSuccess(t('variants.messages.updateSuccess'))
      } else {
        await createVariant(productId, apiData)
        onSuccess(t('variants.messages.createSuccess'))
      }
      
      onClose()
    } catch (error: any) {
      const errorMessage = error?.message || 
        (isEditing ? t('variants.messages.updateError') : t('variants.messages.createError'))
      onError(errorMessage)
    } finally {
      setSubmitting(false)
    }
  }, [
    formData,
    isEditing,
    variant,
    productId,
    createVariant,
    updateVariant,
    onSuccess,
    onError,
    onClose,
    t
  ])
  
  // ========== 4. EFFECTS ==========
  
  /**
   * ✅ Initialize form data when variant changes
   */
  useEffect(() => {
    if (variant) {
      setFormData({
        name: variant.name || '',
        code: variant.code || '',
        sku: variant.sku || '',
        barcode: variant.barcode || '',
        price: variant.price?.toString() || '',
        costPrice: variant.costPrice?.toString() || '',
        displayOrder: variant.displayOrder?.toString() || '0',
        active: variant.active !== false
      })
    } else {
      setFormData(defaultVariantFormData)
    }
    
    setErrors({})
  }, [variant])
  
  // ========== 5. RENDER HELPERS ==========
  
  /**
   * ✅ Render basic information section
   */
  const renderBasicInfo = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('variants.form.basicInfo')}
      </Typography>

      <Grid container spacing={2}>
        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            label={t('variants.fields.name.label')}
            value={formData.name}
            onChange={handleInputChange('name')}
            error={!!errors.name}
            helperText={errors.name || t('variants.fields.name.helper')}
            placeholder={t('variants.fields.name.placeholder')}
            required
          />
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            label={t('variants.fields.code.label')}
            value={formData.code}
            onChange={handleInputChange('code')}
            error={!!errors.code}
            helperText={errors.code || t('variants.fields.code.helper')}
            placeholder={t('variants.fields.code.placeholder')}
            required
          />
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            label={t('variants.fields.sku.label')}
            value={formData.sku}
            onChange={handleInputChange('sku')}
            error={!!errors.sku}
            helperText={errors.sku || t('variants.fields.sku.helper')}
            placeholder={t('variants.fields.sku.placeholder')}
          />
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            label={t('variants.fields.barcode.label')}
            value={formData.barcode}
            onChange={handleInputChange('barcode')}
            error={!!errors.barcode}
            helperText={errors.barcode || t('variants.fields.barcode.helper')}
            placeholder={t('variants.fields.barcode.placeholder')}
          />
        </Grid>
      </Grid>
    </Box>
  )
  
  /**
   * ✅ Render pricing section
   */
  const renderPricing = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('variants.form.pricing')}
      </Typography>

      <Grid container spacing={2}>
        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            type="number"
            label={t('variants.fields.price.label')}
            value={formData.price}
            onChange={handleInputChange('price')}
            error={!!errors.price}
            helperText={errors.price || t('variants.fields.price.helper')}
            placeholder={t('variants.fields.price.placeholder')}
            required
            InputProps={{
              endAdornment: <InputAdornment position="end">₺</InputAdornment>
            }}
            inputProps={{
              min: 0,
              step: 0.01
            }}
          />
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            type="number"
            label={t('variants.fields.costPrice.label')}
            value={formData.costPrice}
            onChange={handleInputChange('costPrice')}
            error={!!errors.costPrice}
            helperText={errors.costPrice || t('variants.fields.costPrice.helper')}
            placeholder={t('variants.fields.costPrice.placeholder')}
            InputProps={{
              endAdornment: <InputAdornment position="end">₺</InputAdornment>
            }}
            inputProps={{
              min: 0,
              step: 0.01
            }}
          />
        </Grid>
      </Grid>
    </Box>
  )
  
  /**
   * ✅ Render settings section
   */
  const renderSettings = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('variants.form.settings')}
      </Typography>

      <Grid container spacing={2}>
        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            type="number"
            label={t('variants.fields.displayOrder.label')}
            value={formData.displayOrder}
            onChange={handleInputChange('displayOrder')}
            error={!!errors.displayOrder}
            helperText={errors.displayOrder || t('variants.fields.displayOrder.helper')}
            placeholder={t('variants.fields.displayOrder.placeholder')}
            inputProps={{
              min: 0,
              step: 1
            }}
          />
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <Box sx={{ pt: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.active}
                  onChange={handleSwitchChange('active')}
                  color="primary"
                />
              }
              label={t('variants.fields.active.label')}
            />
            <Typography variant="caption" color="text.secondary" display="block">
              {t('variants.fields.active.helper')}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Box>
  )
  
  // ========== 6. MAIN RENDER ==========
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      fullScreen={isMobile}
    >
      <form onSubmit={handleSubmit}>
        <DialogTitle>
          {dialogTitle}
        </DialogTitle>
        
        <DialogContent dividers>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Basic Information */}
            {renderBasicInfo()}

            <Divider />

            {/* Pricing */}
            {renderPricing()}

            <Divider />

            {/* Settings */}
            {renderSettings()}
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button 
            onClick={handleClose}
            disabled={submitting}
          >
            {t('variants.form.cancel')}
          </Button>
          
          <Button
            type="submit"
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={16} /> : undefined}
          >
            {submitButtonText}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  )
}
