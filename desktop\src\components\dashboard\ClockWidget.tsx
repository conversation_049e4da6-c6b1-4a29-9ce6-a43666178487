import React, { useState, useEffect } from 'react'
import { Card, CardContent, Typography, Box } from '@mui/material'
export const ClockWidget: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('tr-TR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Card sx={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      '& .MuiCardContent-root': {
        '&:last-child': { pb: 2 },
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }
    }}>
      <CardContent sx={{ py: 1.5, px: 2 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          height: '100%',
          gap: 0.5
        }}>


          {/* Tarih */}
          <Typography
            variant="caption"
            sx={{
              fontWeight: 'bold',
              opacity: 0.9,
              fontSize: '0.75rem'
            }}
          >
            {formatDate(currentTime)}
          </Typography>

          {/* Saat */}
          <Typography
            variant="h4"
            component="div"
            sx={{
              fontWeight: 'bold',
              fontFamily: 'monospace',
              textShadow: '0 2px 4px rgba(0,0,0,0.2)',
              lineHeight: 1
            }}
          >
            {formatTime(currentTime)}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  )
}
