{"app": {"title": "Restaurant POS System", "subtitle": "Setup completed successfully!", "testButton": "Test <PERSON>"}, "menu": {"dashboard": "Dashboard", "orders": "Orders", "products": "Products", "categories": "Categories", "tables": "Tables", "customers": "Customers", "reports": "Reports", "settings": "Settings", "users": "Users", "inventory": "Inventory", "kitchen": "Kitchen", "cashier": "Cashier", "cariler": "Customers", "takeaway": "Takeaway Orders", "quickSale": "Quick Sale"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome", "notifications": {"title": "Notifications", "newOrder": "New order available!", "orderReady": "Order ready", "tableRequest": "Table request", "kitchenAlert": "Kitchen alert", "showAll": "Show all notifications", "noNotifications": "No new notifications", "markAsRead": "<PERSON> as read"}, "weather": {"title": "Weather", "temperature": "Temperature", "condition": "Condition", "sunny": "<PERSON>", "cloudy": "Cloudy", "rainy": "Rainy", "snowy": "Snowy"}, "clock": {"currentTime": "Current Time", "date": "Date"}}, "header": {"internet": {"label": "Internet", "connected": "Internet connection active", "disconnected": "No internet connection"}, "backend": {"label": "Server", "connected": "Server connection active", "disconnected": "No server connection"}, "user": {"menu": "User menu", "logout": "Logout"}, "language": {"tooltip": "Change language", "turkish": "Türkçe", "english": "English"}, "theme": {"tooltip": "Change theme", "light": "Light theme", "dark": "Dark theme"}}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "print": "Print", "close": "Close", "open": "Open", "yes": "Yes", "no": "No", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "refresh": "Refresh", "saving": "Saving...", "none": "None"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password"}, "orders": {"newOrder": "New Order", "orderNumber": "Order Number", "table": "Table", "customer": "Customer", "total": "Total", "status": "Status", "date": "Date", "pending": "Pending", "preparing": "Preparing", "ready": "Ready", "delivered": "Delivered", "cancelled": "Cancelled"}, "products": {"title": "Products", "subtitle": "Product management and inventory tracking", "name": "Product Name", "code": "Product Code", "price": "Price", "basePrice": "Base Price", "costPrice": "Cost Price", "profitMargin": "<PERSON><PERSON>", "category": "Category", "stock": "Stock", "description": "Description", "shortDescription": "Short Description", "image": "Image", "images": "Images", "barcode": "Barcode", "unit": "Unit", "tax": "Tax", "available": "Available", "sellable": "Sellable", "trackStock": "Track Stock", "criticalStock": "Critical Stock", "preparationTime": "Preparation Time", "calories": "Calories", "allergens": "Allergens", "hasVariants": "<PERSON>", "hasModifiers": "Has Modifiers", "showInMenu": "Show in Menu", "featured": "Featured", "displayOrder": "Display Order", "active": "Active", "createdAt": "Created At", "updatedAt": "Updated At", "list": {"title": "Product List", "empty": "No products found", "emptySearch": "No products match your search criteria", "loading": "Loading products...", "error": "Error loading products", "retry": "Retry", "total": "Total {{count}} products", "selected": "{{count}} products selected"}, "form": {"add": "Add New Product", "edit": "Edit Product", "create": "Create Product", "update": "Update Product", "delete": "Delete Product", "duplicate": "Duplicate Product", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "deleting": "Deleting...", "basicInfo": "Basic Information", "pricing": "Pricing", "categoryTax": "Category & Tax", "additionalInfo": "Additional Information", "settings": "Settings", "validation": {"nameRequired": "Product name is required", "nameMinLength": "Product name must be at least 2 characters", "nameMaxLength": "Product name cannot exceed 200 characters", "codeRequired": "Product code is required", "codeFormat": "Product code can only contain uppercase letters, numbers, hyphens and underscores", "codeMaxLength": "Product code cannot exceed 50 characters", "barcodeMaxLength": "Barcode cannot exceed 50 characters", "priceRequired": "Price is required", "priceMin": "Price must be 0 or positive", "priceMax": "Price is too high", "categoryRequired": "Category selection is required", "taxRequired": "Tax selection is required", "descriptionMaxLength": "Description cannot exceed 1000 characters", "shortDescriptionMaxLength": "Short description cannot exceed 200 characters", "imageUrl": "Invalid image URL", "imageRequired": "Product image is required", "imageSize": "Image size is too large", "imageFormat": "Unsupported image format", "costPriceMin": "Cost price must be 0 or positive", "profitMarginRange": "Profit margin must be between 0-100", "criticalStockMin": "Critical stock must be 0 or positive", "preparationTimeMin": "Preparation time must be 0 or positive", "caloriesMin": "Calories must be 0 or positive"}, "placeholders": {"name": "Enter product name", "code": "PROD_001", "barcode": "Barcode number", "description": "Product description", "shortDescription": "Short description", "imageUrl": "https://example.com/image.jpg", "basePrice": "0.00", "costPrice": "0.00", "profitMargin": "0", "criticalStock": "10", "preparationTime": "5", "calories": "0", "allergens": "Gluten, Dairy"}}, "filters": {"search": "Search products...", "searchPlaceholder": "Search by name, code or barcode", "category": "Category", "allCategories": "All Categories", "status": "Status", "allStatuses": "All Statuses", "available": "Available", "unavailable": "Unavailable", "sellable": "Sellable", "notSellable": "Not Sellable", "featured": "Featured", "hasVariants": "<PERSON>", "hasModifiers": "Has Modifiers", "trackStock": "Stock Tracked", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters"}, "actions": {"add": "Add Product", "edit": "Edit", "delete": "Delete", "duplicate": "Duplicate", "view": "View", "export": "Export", "import": "Import", "bulkEdit": "Bulk Edit", "bulkDelete": "Bulk Delete"}, "messages": {"createSuccess": "Product created successfully", "updateSuccess": "Product updated successfully", "deleteSuccess": "Product deleted successfully", "loadSuccess": "Products refreshed successfully", "deleteConfirm": "Are you sure you want to delete this product?", "deleteWarning": "This action cannot be undone", "bulkDeleteConfirm": "Are you sure you want to delete {{count}} selected products?", "createError": "Error creating product", "updateError": "Error updating product", "deleteError": "Error deleting product", "loadError": "Error loading products", "networkError": "Network error", "serverError": "Server error", "validationError": "Form validation error", "duplicateCode": "This product code is already in use", "duplicateBarcode": "This barcode is already in use"}, "units": {"PIECE": "Piece", "KG": "Kilogram", "GRAM": "Gram", "LITER": "Liter", "ML": "Milliliter", "PORTION": "Portion", "BOX": "Box", "PACKAGE": "Package"}, "table": {"columns": {"image": "Image", "name": "Product Name", "code": "Code", "category": "Category", "price": "Price", "stock": "Stock", "status": "Status", "actions": "Actions"}, "noImage": "No Image", "inStock": "In Stock", "outOfStock": "Out of Stock", "lowStock": "Low Stock"}, "pagination": {"rowsPerPage": "Rows per page:", "of": "of", "page": "Page", "first": "First", "previous": "Previous", "next": "Next", "last": "Last"}}, "variants": {"title": "Variants", "subtitle": "Manage product variants", "name": "Variant Name", "code": "Variant Code", "sku": "SKU", "barcode": "Barcode", "price": "Price", "costPrice": "Cost Price", "displayOrder": "Display Order", "active": "Active", "inactive": "Inactive", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "list": {"title": "Variant List", "empty": "No variants found", "emptySearch": "No variants found matching your search criteria", "loading": "Loading variants...", "error": "Error loading variants", "retry": "Retry", "total": "Total {{count}} variants", "selected": "{{count}} variants selected", "noVariants": "No variants defined for this product yet", "addFirstVariant": "Add the first variant"}, "form": {"add": "Add New Variant", "edit": "<PERSON>", "create": "C<PERSON> <PERSON><PERSON><PERSON>", "update": "Update <PERSON><PERSON><PERSON>", "delete": "Delete Variant", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "deleting": "Deleting...", "basicInfo": "Basic Information", "pricing": "Pricing", "settings": "Settings"}, "fields": {"name": {"label": "Variant Name", "placeholder": "e.g. Small Size, Large Size", "helper": "Display name of the variant", "required": "Variant name is required"}, "code": {"label": "Variant Code", "placeholder": "e.g. S, M, L, XL", "helper": "Unique variant code", "required": "Variant code is required"}, "sku": {"label": "SKU", "placeholder": "Stock Keeping Unit", "helper": "Unique code for inventory tracking"}, "barcode": {"label": "Barcode", "placeholder": "Barcode number", "helper": "Product barcode"}, "price": {"label": "Sale Price", "placeholder": "0.00", "helper": "Selling price of the variant", "required": "Price is required"}, "costPrice": {"label": "Cost Price", "placeholder": "0.00", "helper": "Cost price of the variant"}, "displayOrder": {"label": "Display Order", "placeholder": "0", "helper": "Order in which variants are displayed"}, "active": {"label": "Active Status", "helper": "Whether the variant is available for sale"}}, "actions": {"add": "<PERSON><PERSON>", "edit": "Edit", "delete": "Delete", "view": "View", "activate": "Activate", "deactivate": "Deactivate"}, "table": {"columns": {"name": "Name", "code": "Code", "sku": "SKU", "barcode": "Barcode", "price": "Price", "costPrice": "Cost", "status": "Status", "actions": "Actions"}}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> created successfully", "updateSuccess": "<PERSON><PERSON><PERSON> updated successfully", "deleteSuccess": "<PERSON><PERSON><PERSON> deleted successfully", "loadSuccess": "Variants refreshed successfully", "deleteConfirm": "Are you sure you want to delete this variant?", "deleteWarning": "This action cannot be undone", "createError": "Error creating variant", "updateError": "Error updating variant", "deleteError": "Error deleting variant", "loadError": "Error loading variants", "validationError": "Form validation error", "duplicateCode": "This variant code is already in use", "duplicateSku": "This SKU is already in use", "duplicateBarcode": "This barcode is already in use"}}, "settings": {"general": "General", "language": "Language", "theme": "Theme", "currency": "<PERSON><PERSON><PERSON><PERSON>", "tax": "Tax", "printer": "Printer", "backup": "Backup"}}