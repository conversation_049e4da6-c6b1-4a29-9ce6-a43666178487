import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { AuthUser, LoginRequest, LoginResponse } from '@shared/types'

interface AuthState {
  // State
  user: AuthUser | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  rememberMe: boolean

  // Actions
  login: (credentials: LoginRequest, remember?: boolean) => Promise<void>
  logout: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
  checkAuth: () => boolean
  refreshAuth: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      rememberMe: false,

      // Login Action
      login: async (credentials: LoginRequest, remember = false) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(credentials),
          })

          const result = await response.json()

          if (!result.success) {
            throw new Error(result.error || 'Giriş başarısız')
          }

          const loginData: LoginResponse = result.data

          // Store auth data
          set({
            user: {
              userId: loginData.user.id,
              username: loginData.user.username,
              role: loginData.user.role,
              companyId: loginData.user.companyId,
              branchId: loginData.user.branchId,
            },
            token: loginData.token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
            rememberMe: remember,
          })

          // Set token in localStorage for API calls
          localStorage.setItem('auth_token', loginData.token)

        } catch (error) {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Bilinmeyen hata',
          })
          
          // Clear token from localStorage
          localStorage.removeItem('auth_token')
          throw error
        }
      },

      // Logout Action
      logout: async () => {
        set({ isLoading: true })
        
        try {
          const token = get().token
          if (token) {
            // Call logout endpoint
            await fetch('/api/auth/logout', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            })
          }
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          // Clear all auth data
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
            rememberMe: false,
          })
          
          // Clear token from localStorage
          localStorage.removeItem('auth_token')
        }
      },

      // Clear Error
      clearError: () => {
        set({ error: null })
      },

      // Set Loading
      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // Check Auth Status
      checkAuth: () => {
        const { token, user } = get()
        return !!(token && user)
      },

      // Refresh Auth (check if token is still valid)
      refreshAuth: async () => {
        const token = localStorage.getItem('auth_token')
        
        if (!token) {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          })
          return
        }

        try {
          const response = await fetch('/api/auth/profile', {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          })

          if (!response.ok) {
            throw new Error('Token geçersiz')
          }

          const result = await response.json()
          
          if (result.success && result.data) {
            set({
              user: {
                userId: result.data.id,
                username: result.data.username,
                role: result.data.role,
                companyId: result.data.company?.id || '',
                branchId: result.data.branch?.id || '',
              },
              token,
              isAuthenticated: true,
            })
          } else {
            throw new Error('Profil alınamadı')
          }
        } catch (error) {
          // Token geçersiz, temizle
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          })
          localStorage.removeItem('auth_token')
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // Token'ı her zaman persist et (12 saatlik token sistemi için)
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
        rememberMe: state.rememberMe,
      }),
    }
  )
)
