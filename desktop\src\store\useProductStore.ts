// Product Store - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 15: Zustand Store Pattern
// ✅ Kural 8: Error Handling
// ✅ Kural 7: API Integration

import { create } from 'zustand'
import { 
  Product, 
  ProductQueryInput, 
  CreateProductRequest, 
  UpdateProductRequest,
  Category,
  Tax
} from '@shared/types/ProductTypes'
import { productService, AppError } from '../services/productService'
import { DEFAULT_PAGINATION } from '@shared/constants'

// ✅ Kural 15: Store interface
interface ProductStore {
  // State
  products: Product[]
  categories: Category[]
  taxes: Tax[]
  currentProduct: Product | null
  loading: boolean
  error: string | null
  
  // Pagination state
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  
  // Filter state
  filters: ProductQueryInput
  
  // Actions
  fetchProducts: (query?: Partial<ProductQueryInput>) => Promise<void>
  fetchProductById: (id: string) => Promise<void>
  createProduct: (data: CreateProductRequest) => Promise<void>
  updateProduct: (id: string, data: UpdateProductRequest) => Promise<void>
  deleteProduct: (id: string) => Promise<void>
  
  // Categories and taxes
  fetchCategories: () => Promise<void>
  fetchTaxes: () => Promise<void>
  
  // State management
  setCurrentProduct: (product: Product | null) => void
  setFilters: (filters: Partial<ProductQueryInput>) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  
  // Utility actions
  refreshProducts: () => Promise<void>
  resetStore: () => void
}

// ✅ Default query values
const defaultQuery: ProductQueryInput = {
  page: DEFAULT_PAGINATION.PAGE,
  limit: DEFAULT_PAGINATION.LIMIT,
  search: '',
  sortBy: 'name',
  sortOrder: 'asc'
}

// ✅ Kural 15: Zustand store implementation
export const useProductStore = create<ProductStore>((set, get) => ({
  // ========== INITIAL STATE ==========
  products: [],
  categories: [],
  taxes: [],
  currentProduct: null,
  loading: false,
  error: null,
  
  pagination: {
    page: DEFAULT_PAGINATION.PAGE,
    limit: DEFAULT_PAGINATION.LIMIT,
    total: 0,
    totalPages: 0
  },
  
  filters: defaultQuery,
  
  // ========== PRODUCT ACTIONS ==========
  
  /**
   * ✅ Fetch products with pagination and filters
   */
  fetchProducts: async (query?: Partial<ProductQueryInput>) => {
    const state = get()
    const finalQuery = { ...state.filters, ...query }
    
    set({ loading: true, error: null })
    
    try {
      const response = await productService.getProducts(finalQuery)
      
      set({
        products: response.data || [],
        pagination: response.pagination || state.pagination,
        filters: finalQuery,
        loading: false,
        error: null
      })
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Ürünler yüklenirken hata oluştu'
      
      set({
        products: [],
        loading: false,
        error: errorMessage
      })
      
      throw error
    }
  },
  
  /**
   * ✅ Fetch single product by ID
   */
  fetchProductById: async (id: string) => {
    set({ loading: true, error: null })
    
    try {
      const response = await productService.getProductById(id)
      
      set({
        currentProduct: response.data || null,
        loading: false,
        error: null
      })
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Ürün yüklenirken hata oluştu'
      
      set({
        currentProduct: null,
        loading: false,
        error: errorMessage
      })
      
      throw error
    }
  },
  
  /**
   * ✅ Create new product
   */
  createProduct: async (data: CreateProductRequest) => {
    set({ loading: true, error: null })
    
    try {
      const response = await productService.createProduct(data)
      const newProduct = response.data
      
      if (newProduct) {
        // Add to products list
        set(state => ({
          products: [newProduct, ...state.products],
          currentProduct: newProduct,
          loading: false,
          error: null
        }))
        
        // Refresh products to get updated pagination
        await get().refreshProducts()
      }
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Ürün oluşturulurken hata oluştu'
      
      set({
        loading: false,
        error: errorMessage
      })
      
      throw error
    }
  },
  
  /**
   * ✅ Update existing product
   */
  updateProduct: async (id: string, data: UpdateProductRequest) => {
    set({ loading: true, error: null })
    
    try {
      const response = await productService.updateProduct(id, data)
      const updatedProduct = response.data
      
      if (updatedProduct) {
        set(state => ({
          products: state.products.map(product => 
            product.id === id ? updatedProduct : product
          ),
          currentProduct: state.currentProduct?.id === id 
            ? updatedProduct 
            : state.currentProduct,
          loading: false,
          error: null
        }))
      }
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Ürün güncellenirken hata oluştu'
      
      set({
        loading: false,
        error: errorMessage
      })
      
      throw error
    }
  },
  
  /**
   * ✅ Delete product
   */
  deleteProduct: async (id: string) => {
    set({ loading: true, error: null })
    
    try {
      await productService.deleteProduct(id)
      
      set(state => ({
        products: state.products.filter(product => product.id !== id),
        currentProduct: state.currentProduct?.id === id 
          ? null 
          : state.currentProduct,
        loading: false,
        error: null
      }))
      
      // Refresh products to get updated pagination
      await get().refreshProducts()
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Ürün silinirken hata oluştu'
      
      set({
        loading: false,
        error: errorMessage
      })
      
      throw error
    }
  },
  
  // ========== CATEGORIES AND TAXES ==========
  
  /**
   * ✅ Fetch categories
   */
  fetchCategories: async () => {
    try {
      const response = await productService.getCategories()
      
      set({
        categories: response.data || [],
        error: null
      })
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Kategoriler yüklenirken hata oluştu'
      
      set({
        categories: [],
        error: errorMessage
      })
      
      throw error
    }
  },
  
  /**
   * ✅ Fetch taxes
   */
  fetchTaxes: async () => {
    try {
      const response = await productService.getTaxes()
      
      set({
        taxes: response.data || [],
        error: null
      })
    } catch (error) {
      const errorMessage = error instanceof AppError 
        ? error.message 
        : 'Vergiler yüklenirken hata oluştu'
      
      set({
        taxes: [],
        error: errorMessage
      })
      
      throw error
    }
  },
  
  // ========== STATE MANAGEMENT ==========
  
  /**
   * ✅ Set current product
   */
  setCurrentProduct: (product: Product | null) => {
    set({ currentProduct: product })
  },
  
  /**
   * ✅ Set filters
   */
  setFilters: (filters: Partial<ProductQueryInput>) => {
    set(state => ({
      filters: { ...state.filters, ...filters }
    }))
  },
  
  /**
   * ✅ Clear error
   */
  clearError: () => {
    set({ error: null })
  },
  
  /**
   * ✅ Set loading state
   */
  setLoading: (loading: boolean) => {
    set({ loading })
  },
  
  /**
   * ✅ Refresh products with current filters
   */
  refreshProducts: async () => {
    const state = get()
    await state.fetchProducts(state.filters)
  },
  
  /**
   * ✅ Reset store to initial state
   */
  resetStore: () => {
    set({
      products: [],
      categories: [],
      taxes: [],
      currentProduct: null,
      loading: false,
      error: null,
      pagination: {
        page: DEFAULT_PAGINATION.PAGE,
        limit: DEFAULT_PAGINATION.LIMIT,
        total: 0,
        totalPages: 0
      },
      filters: defaultQuery
    })
  }
}))
