// Category Controller - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 6: Controller Layer <PERSON>tern
// ✅ Kural 11: Error Handling Pattern

import { Request, Response, NextFunction } from 'express'
import * as categoryService from '../services/categoryService'
import { AppError, ErrorCodes } from '../utils/AppError'
import { logger } from '../utils/logger'
import { CategoryQueryInput } from '../validators/categoryValidators'

// ✅ Extend Request interface for user data
interface AuthenticatedRequest extends Request {
  user?: {
    userId: string
    username: string
    role: string
    companyId: string
    branchId: string
  }
}

/**
 * ✅ Kural 7: GET /api/categories - Paginated list
 */
export const getCategories = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // ✅ DEVELOPMENT_RULES.md Kural 5: Query parameters are already validated by middleware
    const query = (req as any).validatedQuery as CategoryQueryInput

    logger.info('Getting categories', { 
      companyId, 
      userId: req.user?.userId,
      query 
    })

    const result = await categoryService.getCategories(companyId, query)

    res.json(result)
  } catch (error) {
    logger.error('Get categories controller error', { 
      error, 
      companyId: req.user?.companyId,
      query: req.query 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: GET /api/categories/:id - Single category
 */
export const getCategoryById = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    const { id } = req.params

    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    logger.info('Getting category by ID', { 
      companyId, 
      categoryId: id,
      userId: req.user?.userId 
    })

    const result = await categoryService.getCategoryById(companyId, id)

    res.json(result)
  } catch (error) {
    logger.error('Get category by ID controller error', { 
      error, 
      companyId: req.user?.companyId,
      categoryId: req.params.id 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: POST /api/categories - Create category
 */
export const createCategory = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // ✅ DEVELOPMENT_RULES.md Kural 5: Request body is already validated by middleware
    const data = req.body

    logger.info('Creating category', { 
      companyId, 
      userId: req.user?.userId,
      categoryName: data.name 
    })

    const result = await categoryService.createCategory(companyId, data)

    res.status(201).json(result)
  } catch (error) {
    logger.error('Create category controller error', { 
      error, 
      companyId: req.user?.companyId,
      data: req.body 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: PUT /api/categories/:id - Update category
 */
export const updateCategory = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    const { id } = req.params

    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // ✅ DEVELOPMENT_RULES.md Kural 5: Request body is already validated by middleware
    const data = req.body

    logger.info('Updating category', { 
      companyId, 
      categoryId: id,
      userId: req.user?.userId 
    })

    const result = await categoryService.updateCategory(companyId, id, data)

    res.json(result)
  } catch (error) {
    logger.error('Update category controller error', { 
      error, 
      companyId: req.user?.companyId,
      categoryId: req.params.id,
      data: req.body 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: DELETE /api/categories/:id - Delete category (soft delete)
 */
export const deleteCategory = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    const { id } = req.params

    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    logger.info('Deleting category', { 
      companyId, 
      categoryId: id,
      userId: req.user?.userId 
    })

    const result = await categoryService.deleteCategory(companyId, id)

    res.json(result)
  } catch (error) {
    logger.error('Delete category controller error', { 
      error, 
      companyId: req.user?.companyId,
      categoryId: req.params.id 
    })
    next(error)
  }
}
