// Product Controller - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 6: Controller Layer Pattern
// ✅ Kural 11: Error Handling Pattern

import { Request, Response, NextFunction } from 'express'
import { productService } from '../services/productService'
import { AppError, ErrorCodes } from '../utils/AppError'
import { logger } from '../utils/logger'
import { ProductQueryInput } from '../validators/productValidators'

// ✅ Extend Request interface for user data
interface AuthenticatedRequest extends Request {
  user?: {
    userId: string
    username: string
    role: string
    companyId: string
    branchId: string
  }
}

/**
 * ✅ Kural 7: GET /api/products - Paginated list
 */
export const getProducts = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // Query parameters are already validated by middleware
    const query = (req as any).validatedQuery as ProductQueryInput

    logger.info('Getting products', { 
      companyId, 
      userId: req.user?.userId,
      query 
    })

    const result = await productService.getProducts(companyId, query)

    res.json(result)
  } catch (error) {
    logger.error('Get products controller error', { 
      error, 
      companyId: req.user?.companyId,
      query: req.query 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: GET /api/products/:id - Single product
 */
export const getProductById = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    const { id } = req.params

    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    logger.info('Getting product by ID', { 
      companyId, 
      productId: id,
      userId: req.user?.userId 
    })

    const result = await productService.getProductById(companyId, id)

    res.json(result)
  } catch (error) {
    logger.error('Get product by ID controller error', { 
      error, 
      companyId: req.user?.companyId,
      productId: req.params.id 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: POST /api/products - Create product
 */
export const createProduct = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // Request body is already validated by middleware
    const data = req.body

    logger.info('Creating product', { 
      companyId, 
      userId: req.user?.userId,
      productCode: data.code 
    })

    const result = await productService.createProduct(companyId, data)

    res.status(201).json(result)
  } catch (error) {
    logger.error('Create product controller error', { 
      error, 
      companyId: req.user?.companyId,
      data: req.body 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: PUT /api/products/:id - Update product
 */
export const updateProduct = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    const { id } = req.params

    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    // Request body is already validated by middleware
    const data = req.body

    logger.info('Updating product', { 
      companyId, 
      productId: id,
      userId: req.user?.userId 
    })

    const result = await productService.updateProduct(companyId, id, data)

    res.json(result)
  } catch (error) {
    logger.error('Update product controller error', { 
      error, 
      companyId: req.user?.companyId,
      productId: req.params.id,
      data: req.body 
    })
    next(error)
  }
}

/**
 * ✅ Kural 7: DELETE /api/products/:id - Delete product (soft delete)
 */
export const deleteProduct = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    const { id } = req.params

    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    logger.info('Deleting product', { 
      companyId, 
      productId: id,
      userId: req.user?.userId 
    })

    const result = await productService.deleteProduct(companyId, id)

    res.json(result)
  } catch (error) {
    logger.error('Delete product controller error', { 
      error, 
      companyId: req.user?.companyId,
      productId: req.params.id 
    })
    next(error)
  }
}

/**
 * ✅ Helper endpoints for dropdowns
 */
export const getCategories = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    logger.info('Getting categories', { 
      companyId, 
      userId: req.user?.userId 
    })

    const result = await productService.getCategories(companyId)

    res.json(result)
  } catch (error) {
    logger.error('Get categories controller error', { 
      error, 
      companyId: req.user?.companyId 
    })
    next(error)
  }
}

export const getTaxes = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const companyId = req.user?.companyId
    if (!companyId) {
      throw new AppError(
        'Şirket bilgisi bulunamadı',
        401,
        ErrorCodes.UNAUTHORIZED
      )
    }

    logger.info('Getting taxes', { 
      companyId, 
      userId: req.user?.userId 
    })

    const result = await productService.getTaxes(companyId)

    res.json(result)
  } catch (error) {
    logger.error('Get taxes controller error', { 
      error, 
      companyId: req.user?.companyId 
    })
    next(error)
  }
}
