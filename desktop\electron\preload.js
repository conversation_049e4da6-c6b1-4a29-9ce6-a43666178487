const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
  getVersion: () => ipcRenderer.invoke('app:version'),
  
  // Printer API
  getPrinters: () => ipcRenderer.invoke('get-printers'),
  print: (data) => ipcRenderer.invoke('print', data),
  
  // File API
  selectFile: () => ipcRenderer.invoke('select-file'),
  saveFile: (data) => ipcRenderer.invoke('save-file', data),
  
  // System
  minimize: () => ipcRenderer.send('minimize'),
  maximize: () => ipcRenderer.send('maximize'),
  close: () => ipcRenderer.send('close'),
})
